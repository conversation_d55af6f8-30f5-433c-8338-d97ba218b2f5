/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

:root {
    /* Modern Color Palette */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Accent Colors */
    --purple-500: #8b5cf6;
    --purple-600: #7c3aed;
    --emerald-500: #10b981;
    --emerald-600: #059669;
    --amber-500: #f59e0b;
    --red-500: #ef4444;

    /* Background */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;

    /* Text */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    /* Borders */
    --border-primary: rgba(148, 163, 184, 0.1);
    --border-secondary: rgba(148, 163, 184, 0.2);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* Container */
.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    box-shadow:
        0 4px 14px 0 rgba(2, 132, 199, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px 0 rgba(2, 132, 199, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid var(--border-secondary);
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    backdrop-filter: blur(12px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-500);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: 16px;
}

.btn-subtitle {
    display: block;
    font-size: 12px;
    font-weight: 400;
    opacity: 0.8;
    margin-top: 2px;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--purple-500) 50%, var(--primary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(24px);
    border-bottom: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: var(--border-secondary);
}

.nav-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 16px;
    text-decoration: none;
    color: var(--text-primary);
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 14px rgba(14, 165, 233, 0.25);
}

.brand-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.brand-name {
    font-size: 20px;
    font-weight: 800;
    letter-spacing: -0.5px;
    color: var(--text-primary);
}

.brand-subtitle {
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-400);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 40px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 15px;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-500);
    transition: width 0.3s ease;
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-toggle {
    display: none;
    cursor: pointer;
    color: var(--text-primary);
    width: 24px;
    height: 24px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background:
        radial-gradient(ellipse 80% 50% at 50% 40%, rgba(14, 165, 233, 0.08) 0%, transparent 60%),
        radial-gradient(ellipse 60% 40% at 80% 60%, rgba(139, 92, 246, 0.06) 0%, transparent 60%);
    position: relative;
    overflow: hidden;
    padding: 120px 0 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, transparent 0%, rgba(14, 165, 233, 0.02) 50%, transparent 100%),
        linear-gradient(45deg, transparent 0%, rgba(139, 92, 246, 0.02) 50%, transparent 100%);
    pointer-events: none;
    animation: heroGlow 12s ease-in-out infinite;
}

@keyframes heroGlow {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.hero-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 640px;
}

.hero-announcement {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-primary);
    border-radius: 50px;
    padding: 12px 20px;
    margin-bottom: 32px;
    backdrop-filter: blur(12px);
}

.announcement-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-400);
    font-size: 14px;
    font-weight: 500;
}

.announcement-link {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.announcement-link:hover {
    color: var(--primary-400);
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    color: var(--text-primary);
    letter-spacing: -0.02em;
}

.hero-description {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
    max-width: 580px;
}

.hero-features {
    display: flex;
    gap: 32px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
}

.feature-item i {
    width: 16px;
    height: 16px;
    color: var(--primary-400);
}

.hero-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 48px;
    flex-wrap: wrap;
    align-items: flex-start;
}

.hero-social-proof {
    margin-top: 48px;
}

.social-proof-text {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 16px;
    font-weight: 500;
}

.company-logos {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.logo-placeholder {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.dashboard-preview {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    width: 100%;
    max-width: 500px;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.dashboard-header {
    background: var(--bg-secondary);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-primary);
}

.dashboard-controls {
    display: flex;
    gap: 8px;
}

.dashboard-controls .control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-600);
}

.dashboard-title {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 600;
}

.dashboard-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-500);
}

.status-indicator.active {
    background: var(--emerald-500);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.dashboard-body {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.metric-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 16px;
}

.metric-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.metric-title {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-icon {
    width: 16px;
    height: 16px;
    color: var(--primary-400);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.metric-change {
    font-size: 12px;
    font-weight: 500;
}

.metric-change.positive {
    color: var(--emerald-500);
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.activity-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.activity-indicator.critical {
    background: var(--red-500);
}

.activity-indicator.success {
    background: var(--emerald-500);
}

.activity-indicator.warning {
    background: var(--amber-500);
}

.activity-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.activity-title {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
}

.activity-time {
    font-size: 11px;
    color: var(--text-muted);
}

/* Overview Section */
.overview {
    padding: 120px 0;
    background: linear-gradient(180deg, transparent 0%, rgba(14, 165, 233, 0.02) 100%);
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.overview-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    padding: 32px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(12px);
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.overview-card:hover::before {
    opacity: 1;
}

.overview-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-500);
    box-shadow:
        0 25px 50px rgba(14, 165, 233, 0.15),
        0 0 0 1px rgba(14, 165, 233, 0.1);
}

.overview-card.primary {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
    border-color: var(--primary-500);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.card-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 14px rgba(14, 165, 233, 0.25);
}

.card-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.overview-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
}

.card-metrics {
    display: flex;
    gap: 32px;
}

.metric {
    text-align: center;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-400);
    display: block;
}

.metric-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
}

.feature-list i {
    width: 16px;
    height: 16px;
    color: var(--emerald-500);
}

.progress-indicator {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500) 0%, var(--emerald-500) 100%);
    border-radius: 4px;
    transition: width 2s ease;
}

.progress-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

.team-avatars {
    display: flex;
    align-items: center;
    gap: -8px;
    margin-top: 16px;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--purple-500) 100%);
    border: 2px solid var(--bg-primary);
    margin-left: -8px;
}

.avatar:first-child {
    margin-left: 0;
}

.avatar-count {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 600;
    margin-left: -8px;
}

/* Platform Section */
.platform {
    padding: 120px 0;
    background: var(--bg-secondary);
}

.platform-header {
    text-align: center;
    margin-bottom: 80px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(14, 165, 233, 0.1);
    border: 1px solid rgba(14, 165, 233, 0.2);
    color: var(--primary-400);
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.section-description {
    font-size: 18px;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.platform-architecture {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 80px;
}

.architecture-layer {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    padding: 32px;
    transition: all 0.3s ease;
    backdrop-filter: blur(12px);
}

.architecture-layer:hover {
    border-color: var(--primary-500);
    transform: translateX(8px);
    box-shadow: 0 20px 40px rgba(14, 165, 233, 0.1);
}

.layer-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
}

.layer-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 14px rgba(14, 165, 233, 0.25);
}

.layer-info h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.layer-info p {
    color: var(--text-secondary);
    margin: 0;
}

.layer-components {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.component {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
}

.component:hover {
    background: rgba(14, 165, 233, 0.05);
    border-color: var(--primary-500);
}

.component i {
    width: 20px;
    height: 20px;
    color: var(--primary-400);
}

.component span {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.platform-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border-primary);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(12px);
}

.stat-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary-500);
    box-shadow: 0 20px 40px rgba(14, 165, 233, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 14px rgba(14, 165, 233, 0.25);
}

.stat-content .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-content .stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }

    .hero-features {
        justify-content: center;
    }

    .overview-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .platform-architecture {
        gap: 20px;
    }

    .layer-components {
        grid-template-columns: 1fr;
    }

    .platform-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: block;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-container {
        gap: 40px;
    }

    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 16px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        width: 100%;
    }

    .hero-actions .btn-large {
        width: 100%;
        justify-content: center;
    }

    .company-logos {
        justify-content: center;
    }

    .dashboard-preview {
        max-width: 100%;
        transform: none;
    }

    .overview {
        padding: 80px 0;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .platform {
        padding: 80px 0;
    }

    .platform-header {
        margin-bottom: 60px;
    }

    .architecture-layer {
        padding: 24px;
    }

    .layer-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .layer-components {
        grid-template-columns: 1fr;
    }

    .platform-stats {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .nav-container {
        padding: 0 16px;
        height: 70px;
    }

    .brand-logo {
        gap: 12px;
    }

    .brand-icon {
        width: 32px;
        height: 32px;
    }

    .brand-name {
        font-size: 18px;
    }

    .hero-announcement {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 18px;
    }

    .overview-card {
        padding: 24px;
    }

    .card-metrics {
        gap: 20px;
    }

    .architecture-layer {
        padding: 20px;
    }

    .layer-icon {
        width: 48px;
        height: 48px;
    }

    .stat-card {
        padding: 20px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth scrolling */
html {
    scroll-padding-top: 80px;
}

.terminal-window {
    background: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
    max-width: 500px;
}

.terminal-header {
    background: #2a2a2a;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.terminal-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ff5f56; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #27ca3f; }

.terminal-title {
    font-size: 14px;
    color: #a0a0a0;
    font-weight: 500;
}

.terminal-body {
    padding: 20px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.terminal-line {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.prompt {
    color: #3b82f6;
    font-weight: 600;
}

.command {
    color: #ffffff;
}

.output {
    color: #a0a0a0;
}

.output.success {
    color: #10b981;
}

.output.warning {
    color: #f59e0b;
}

.cursor {
    color: #3b82f6;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Platform Section */
.platform {
    padding: 120px 0;
    background: linear-gradient(180deg, transparent 0%, rgba(59, 130, 246, 0.02) 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    color: white;
    margin-bottom: 24px;
}

.section-description {
    font-size: 18px;
    color: #a0a0a0;
    line-height: 1.6;
}

.platform-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
}

.platform-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 32px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.platform-card:hover {
    transform: translateY(-8px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
}

.card-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #00ff41 0%, #00cc33 100%);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: #000;
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.2);
    animation: cyberIconGlow 3s ease-in-out infinite;
}

@keyframes cyberIconGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(0, 255, 65, 0.2);
        border-color: rgba(0, 255, 65, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
        border-color: rgba(0, 255, 65, 0.4);
    }
}

.card-icon i {
    width: 32px;
    height: 32px;
}

.card-title {
    font-size: 20px;
    font-weight: 700;
    color: white;
    margin-bottom: 16px;
}

.card-description {
    color: #a0a0a0;
    line-height: 1.6;
}

/* Capabilities Section */
.capabilities {
    padding: 120px 0;
    background: rgba(10, 10, 10, 0.8);
}

.capabilities-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.capabilities-text {
    max-width: 600px;
}

.capability-list {
    margin-top: 40px;
}

.capability-item {
    display: flex;
    gap: 16px;
    margin-bottom: 32px;
    align-items: flex-start;
}

.capability-item i {
    width: 24px;
    height: 24px;
    color: #10b981;
    flex-shrink: 0;
    margin-top: 4px;
}

.capability-item h4 {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.capability-item p {
    color: #a0a0a0;
    margin: 0;
}

/* Benchmark Chart */
.capabilities-visual {
    display: flex;
    justify-content: center;
}

.benchmark-chart {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 32px;
    width: 100%;
    max-width: 500px;
    backdrop-filter: blur(10px);
}

.chart-header {
    text-align: center;
    margin-bottom: 32px;
}

.chart-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: white;
    margin-bottom: 8px;
}

.chart-header p {
    color: #a0a0a0;
    margin: 0;
}

.chart-bars {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-bar {
    display: flex;
    align-items: center;
    gap: 16px;
}

.bar-label {
    font-size: 14px;
    font-weight: 500;
    color: #a0a0a0;
    min-width: 120px;
    text-align: right;
}

.bar-container {
    flex: 1;
    height: 32px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 16px;
    transition: width 2s ease;
    position: relative;
}

.bar-fill.traditional {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.bar-value {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    font-weight: 600;
    color: white;
}

/* CTA Section */
.cta {
    padding: 120px 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: white;
    margin-bottom: 24px;
}

.cta-description {
    font-size: 18px;
    color: #a0a0a0;
    margin-bottom: 40px;
    line-height: 1.6;
}

.cta-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    background: #0a0a0a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 80px 0 32px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 64px;
    margin-bottom: 48px;
}

.footer-brand {
    max-width: 400px;
}

.footer-description {
    color: #a0a0a0;
    margin-top: 16px;
    line-height: 1.6;
}

.footer-column h4 {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
}

.footer-column a {
    display: block;
    color: #a0a0a0;
    text-decoration: none;
    margin-bottom: 12px;
    transition: color 0.3s ease;
    font-size: 14px;
}

.footer-column a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 32px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #a0a0a0;
    font-size: 14px;
}

.footer-legal {
    display: flex;
    gap: 24px;
}

.footer-legal a {
    color: #a0a0a0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 48px;
        text-align: center;
    }

    .capabilities-content {
        grid-template-columns: 1fr;
        gap: 48px;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 48px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: block;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .platform-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .nav-container {
        padding: 0 16px;
    }

    .hero-container {
        padding: 0 16px;
    }

    .terminal-window {
        max-width: 100%;
    }

    .terminal-body {
        padding: 16px;
        font-size: 12px;
    }

    .platform-card {
        padding: 24px;
    }

    .benchmark-chart {
        padding: 24px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.platform-card {
    animation: fadeInUp 0.6s ease forwards;
}

.platform-card:nth-child(1) { animation-delay: 0.1s; }
.platform-card:nth-child(2) { animation-delay: 0.2s; }
.platform-card:nth-child(3) { animation-delay: 0.3s; }
.platform-card:nth-child(4) { animation-delay: 0.4s; }
.platform-card:nth-child(5) { animation-delay: 0.5s; }
.platform-card:nth-child(6) { animation-delay: 0.6s; }

/* Smooth scrolling for anchor links */
html {
    scroll-padding-top: 70px;
}

/* 3D Background */
.background-3d {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 65, 0.01) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.01) 0%, transparent 50%);
    animation: neuralPulse 12s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% {
        background:
            radial-gradient(circle at 20% 80%, rgba(0, 255, 65, 0.01) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.01) 0%, transparent 50%);
    }
    50% {
        background:
            radial-gradient(circle at 30% 70%, rgba(0, 255, 65, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(0, 255, 65, 0.02) 0%, transparent 50%);
    }
}

/* Hero 3D Container */
.hero-3d-container {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 300px;
    height: 300px;
    z-index: 0;
    pointer-events: none;
}

/* Platform Card 3D Containers */
.card-3d-container {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    z-index: 0;
    pointer-events: none;
    opacity: 0.7;
}

.platform-card {
    position: relative;
    overflow: visible;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.platform-card:hover {
    transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
}

.platform-card:hover .card-3d-container {
    opacity: 1;
}

/* 3D Card Effects */
.platform-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border-radius: 16px;
    transform: translateZ(-10px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.platform-card:hover::before {
    opacity: 1;
}

/* Terminal 3D Effects */
.terminal-window {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.terminal-window:hover {
    transform: rotateX(5deg) rotateY(-5deg) translateZ(20px);
}

/* Hero Visual 3D Layout */
.hero-visual {
    position: relative;
    transform-style: preserve-3d;
    perspective: 1200px;
    overflow: hidden;
}

.hero-visual::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 255, 65, 0.02) 0%, transparent 30%),
        radial-gradient(circle at 70% 70%, rgba(0, 255, 65, 0.01) 0%, transparent 30%);
    animation: neuralFlow 15s linear infinite;
    pointer-events: none;
    z-index: 0;
}

@keyframes neuralFlow {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.05); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Floating Animation for 3D Elements */
@keyframes float3D {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg) rotateY(0deg);
    }
    33% {
        transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
    }
    66% {
        transform: translateY(-5px) rotateX(-3deg) rotateY(-3deg);
    }
}

.card-3d-container {
    animation: float3D 6s ease-in-out infinite;
}

.card-3d-container:nth-child(1) { animation-delay: 0s; }
.card-3d-container:nth-child(2) { animation-delay: 1s; }
.card-3d-container:nth-child(3) { animation-delay: 2s; }
.card-3d-container:nth-child(4) { animation-delay: 3s; }
.card-3d-container:nth-child(5) { animation-delay: 4s; }
.card-3d-container:nth-child(6) { animation-delay: 5s; }

/* 3D Glow Effects */
.platform-card:hover .card-icon {
    transform: translateZ(20px);
    filter: drop-shadow(0 10px 20px rgba(59, 130, 246, 0.3));
}

.card-icon {
    transition: transform 0.3s ease, filter 0.3s ease;
    transform-style: preserve-3d;
}

/* Responsive 3D */
@media (max-width: 768px) {
    .hero-3d-container {
        width: 200px;
        height: 200px;
        top: -30px;
        right: -30px;
    }

    .card-3d-container {
        width: 80px;
        height: 80px;
        top: -15px;
        right: -15px;
    }

    .platform-card:hover {
        transform: translateY(-5px) rotateX(2deg) rotateY(2deg);
    }
}

/* Advanced 3D Effects */
.hero-visual {
    filter: drop-shadow(0 20px 40px rgba(59, 130, 246, 0.2));
}

.terminal-window {
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.terminal-window:hover {
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.6),
        0 0 0 1px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 50px rgba(59, 130, 246, 0.2);
}

/* 3D Card Glow Effects */
.platform-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.platform-card:hover {
    box-shadow:
        0 20px 40px rgba(59, 130, 246, 0.15),
        0 0 0 1px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Floating Animation Variants */
@keyframes float3DVariant1 {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg) rotateZ(0deg);
    }
    50% {
        transform: translateY(-15px) rotateX(10deg) rotateZ(5deg);
    }
}

@keyframes float3DVariant2 {
    0%, 100% {
        transform: translateY(0px) rotateY(0deg) rotateZ(0deg);
    }
    50% {
        transform: translateY(-10px) rotateY(15deg) rotateZ(-5deg);
    }
}

.card-3d-container:nth-child(odd) {
    animation: float3DVariant1 8s ease-in-out infinite;
}

.card-3d-container:nth-child(even) {
    animation: float3DVariant2 7s ease-in-out infinite;
}

/* Hero 3D Scene Enhancements */
.hero-3d-container canvas {
    border-radius: 20px;
    filter: drop-shadow(0 10px 30px rgba(59, 130, 246, 0.3));
}

/* Interactive 3D Cursor */
.platform-card {
    cursor: pointer;
}

.platform-card:active {
    transform: translateY(-4px) rotateX(2deg) rotateY(2deg) scale(0.98);
}

/* 3D Text Effects */
.hero-title {
    text-shadow:
        0 0 5px rgba(0, 255, 65, 0.3),
        0 0 10px rgba(0, 255, 65, 0.2);
    animation: matrixTitleGlow 4s ease-in-out infinite;
}

@keyframes matrixTitleGlow {
    0%, 100% {
        text-shadow:
            0 0 5px rgba(0, 255, 65, 0.3),
            0 0 10px rgba(0, 255, 65, 0.2);
    }
    50% {
        text-shadow:
            0 0 8px rgba(0, 255, 65, 0.4),
            0 0 15px rgba(0, 255, 65, 0.3);
    }
}

.section-title {
    text-shadow:
        0 0 3px rgba(0, 255, 65, 0.3),
        0 0 6px rgba(0, 255, 65, 0.2);
}

/* 3D Button Effects */
.btn-primary {
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn-primary:hover {
    transform: translateY(-3px) translateZ(10px);
    box-shadow:
        0 15px 30px rgba(59, 130, 246, 0.4),
        0 0 0 1px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
    transform: translateY(-1px) translateZ(5px);
}

.btn-secondary:hover {
    transform: translateY(-3px) translateZ(10px);
    box-shadow:
        0 15px 30px rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Tools Arsenal Section */
.tools-arsenal {
    padding: 120px 0;
    background: linear-gradient(180deg, rgba(10, 10, 10, 0.8) 0%, rgba(59, 130, 246, 0.02) 100%);
}

.tools-categories {
    display: flex;
    flex-direction: column;
    gap: 80px;
}

.tool-category {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.tool-category:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 40px;
    padding-bottom: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #00ff41 0%, #00cc33 100%);
    border: 1px solid rgba(0, 255, 65, 0.4);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    flex-shrink: 0;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.2);
    animation: cyberCategoryGlow 4s ease-in-out infinite;
}

@keyframes cyberCategoryGlow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(0, 255, 65, 0.2);
        border-color: rgba(0, 255, 65, 0.4);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        border-color: rgba(0, 255, 65, 0.5);
    }
}

.category-icon i {
    width: 40px;
    height: 40px;
}

.category-title {
    font-size: 28px;
    font-weight: 700;
    color: white;
    margin: 0 0 8px 0;
}

.category-description {
    color: #a0a0a0;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.tool-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
    transform-style: preserve-3d;
}

.tool-item:hover {
    transform: translateY(-5px) rotateX(2deg);
    border-color: rgba(0, 255, 65, 0.4);
    box-shadow:
        0 15px 30px rgba(0, 255, 65, 0.1),
        0 0 0 1px rgba(0, 255, 65, 0.2),
        0 0 20px rgba(0, 255, 65, 0.1);
}

.tool-item .tool-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #00ff41 0%, #00cc33 100%);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    margin-bottom: 16px;
    transition: transform 0.3s ease;
    box-shadow: 0 0 8px rgba(0, 255, 65, 0.2);
}

.tool-item:hover .tool-icon {
    transform: translateZ(10px) scale(1.1);
}

.tool-item .tool-icon i {
    width: 24px;
    height: 24px;
}

.tool-item h4 {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin: 0 0 8px 0;
}

.tool-item p {
    color: #a0a0a0;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 16px 0;
}

.tool-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: rgba(0, 255, 65, 0.05);
    border: 1px solid rgba(0, 255, 65, 0.3);
    color: #00ff41;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 0 5px rgba(0, 255, 65, 0.1);
    animation: cyberTagGlow 3s ease-in-out infinite;
}

@keyframes cyberTagGlow {
    0%, 100% {
        border-color: rgba(0, 255, 65, 0.3);
        color: #00ff41;
        box-shadow: 0 0 5px rgba(0, 255, 65, 0.1);
    }
    50% {
        border-color: rgba(0, 255, 65, 0.4);
        color: #00ff41;
        box-shadow: 0 0 8px rgba(0, 255, 65, 0.2);
    }
}

/* Responsive Design for Tools Arsenal */
@media (max-width: 768px) {
    .tools-arsenal {
        padding: 80px 0;
    }

    .tools-categories {
        gap: 60px;
    }

    .tool-category {
        padding: 24px;
    }

    .category-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .category-icon i {
        width: 30px;
        height: 30px;
    }

    .category-title {
        font-size: 24px;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }
}
