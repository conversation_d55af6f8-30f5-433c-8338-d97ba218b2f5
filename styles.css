/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

:root {
    /* Color Palette - Clean and Professional */
    --primary-black: #000000;
    --primary-white: #ffffff;
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #e5e5e5;
    --gray-300: #d4d4d4;
    --gray-400: #a3a3a3;
    --gray-500: #737373;
    --gray-600: #525252;
    --gray-700: #404040;
    --gray-800: #262626;
    --gray-900: #171717;
    
    /* Accent Colors */
    --blue-500: #3b82f6;
    --green-500: #22c55e;
    --red-500: #ef4444;
    --yellow-500: #eab308;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 120px;
    
    /* Borders */
    --border-radius: 8px;
    --border-radius-lg: 16px;
    --border-color: var(--gray-200);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--primary-black);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow-x: hidden;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--primary-black);
}

p {
    margin-bottom: 1rem;
    color: var(--gray-600);
    line-height: 1.7;
}

/* Buttons */
.btn-primary {
    background: var(--primary-black);
    color: var(--primary-white);
    border: 2px solid var(--primary-black);
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    font-family: var(--font-family);
}

.btn-primary:hover {
    background: var(--primary-white);
    color: var(--primary-black);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background: var(--primary-white);
    color: var(--primary-black);
    border: 2px solid var(--gray-300);
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    font-family: var(--font-family);
}

.btn-secondary:hover {
    border-color: var(--primary-black);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: var(--border-radius-lg);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--gray-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(24px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: var(--primary-black);
}

.brand-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-black);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
}

.brand-text {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 40px;
}

.nav-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    font-size: 15px;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-black);
    transition: width 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-black);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-toggle {
    display: none;
    cursor: pointer;
    color: var(--primary-black);
    width: 24px;
    height: 24px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--primary-white);
    position: relative;
    overflow: hidden;
    padding: 120px 0 80px;
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-600);
    margin-bottom: 32px;
    backdrop-filter: blur(12px);
}

.badge-indicator {
    width: 8px;
    height: 8px;
    background: var(--green-500);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    color: var(--primary-black);
    letter-spacing: -0.02em;
}

.hero-description {
    font-size: 20px;
    color: var(--gray-600);
    margin-bottom: 32px;
    line-height: 1.6;
    max-width: 540px;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: left;
}

.stat-number {
    font-size: 32px;
    font-weight: 800;
    color: var(--primary-black);
    display: block;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--gray-500);
    font-weight: 500;
    margin-top: 4px;
}

.hero-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 48px;
    flex-wrap: wrap;
}

.hero-trust {
    margin-top: 48px;
}

.trust-text {
    font-size: 14px;
    color: var(--gray-500);
    margin-bottom: 16px;
    font-weight: 500;
}

.trust-logos {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.trust-logo {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    font-size: 12px;
    color: var(--gray-500);
    font-weight: 500;
    backdrop-filter: blur(12px);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.security-dashboard {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 480px;
    backdrop-filter: blur(20px);
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(12px);
}

.dashboard-controls {
    display: flex;
    gap: 8px;
}

.dashboard-controls .control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: var(--red-500); }
.control.yellow { background: var(--yellow-500); }
.control.green { background: var(--green-500); }

.dashboard-title {
    font-size: 14px;
    color: var(--primary-black);
    font-weight: 600;
}

.dashboard-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--gray-600);
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-400);
}

.status-dot.active {
    background: var(--green-500);
    animation: pulse 2s infinite;
}

.dashboard-content {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.security-metric {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    padding: 16px;
    backdrop-filter: blur(12px);
}

.metric-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.metric-title {
    font-size: 14px;
    color: var(--gray-600);
    font-weight: 500;
}

.metric-icon {
    width: 16px;
    height: 16px;
    color: var(--gray-400);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 4px;
}

.metric-trend {
    font-size: 12px;
    font-weight: 500;
}

.metric-trend.positive { color: var(--green-500); }
.metric-trend.negative { color: var(--red-500); }

.activity-log {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.log-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.log-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.log-indicator.critical { background: var(--red-500); }
.log-indicator.success { background: var(--green-500); }
.log-indicator.warning { background: var(--yellow-500); }

.log-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.log-title {
    font-size: 13px;
    color: var(--primary-black);
    font-weight: 500;
}

.log-time {
    font-size: 11px;
    color: var(--gray-500);
}

/* Platform Section */
.platform {
    padding: var(--section-padding) 0;
    background: transparent;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--gray-600);
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
    backdrop-filter: blur(12px);
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: var(--primary-black);
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.section-description {
    font-size: 18px;
    color: var(--gray-600);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.platform-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
}

.platform-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 32px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.platform-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    border-color: var(--gray-300);
}

.platform-card.featured {
    border-color: rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(24px);
}

.card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 20px;
}

.card-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-black);
    margin: 0;
}

.card-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-black);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    margin-bottom: 16px;
}

.card-badge {
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 24px;
}

.card-features {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray-600);
    font-size: 14px;
    font-weight: 500;
}

.feature i {
    width: 16px;
    height: 16px;
    color: var(--green-500);
}

.card-metrics {
    display: flex;
    gap: 32px;
    margin-top: 24px;
}

.metric {
    text-align: left;
}

.metric-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-black);
    display: block;
    line-height: 1;
}

.metric-label {
    font-size: 12px;
    color: var(--gray-500);
    font-weight: 500;
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.compliance-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 24px;
}

.compliance-badge {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--gray-600);
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(12px);
}

/* Solutions Section */
.solutions {
    padding: var(--section-padding) 0;
    background: transparent;
}

.solutions-tabs {
    max-width: 1000px;
    margin: 0 auto;
}

.tab-nav {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 60px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px;
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-button {
    background: transparent;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--gray-600);
    font-size: 14px;
    white-space: nowrap;
    font-family: var(--font-family);
}

.tab-button.active {
    background: rgba(255, 255, 255, 0.3);
    color: var(--primary-black);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(12px);
}

.tab-button:hover:not(.active) {
    color: var(--primary-black);
}

.tab-content {
    position: relative;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.solution-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.solution-text h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-black);
    margin-bottom: 16px;
}

.solution-text p {
    font-size: 18px;
    color: var(--gray-600);
    margin-bottom: 24px;
    line-height: 1.6;
}

.solution-features {
    list-style: none;
    padding: 0;
    margin: 0 0 32px 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.solution-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--gray-600);
    font-size: 16px;
    font-weight: 500;
}

.solution-features i {
    width: 20px;
    height: 20px;
    color: var(--green-500);
}

.solution-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.enterprise-diagram {
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: center;
}

.diagram-node {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    min-width: 200px;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
}

.diagram-node:hover {
    border-color: var(--primary-black);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.diagram-node i {
    width: 32px;
    height: 32px;
    color: var(--primary-black);
}

.diagram-node span {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-black);
    text-align: center;
}

/* 3D Background */
.background-3d {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.hero-3d-container {
    width: 100%;
    height: 400px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }

    .solution-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .platform-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.1);
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding: 24px;
        flex-direction: column;
        gap: 24px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(24px);
    }

    .nav-menu.mobile-active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-actions {
        flex-direction: column;
        width: 100%;
    }

    .nav-actions .btn-primary,
    .nav-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .nav-toggle {
        display: block;
    }

    body.nav-open {
        overflow: hidden;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-container {
        gap: 40px;
    }

    .hero-stats {
        justify-content: center;
        gap: 24px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        width: 100%;
    }

    .hero-actions .btn-large {
        width: 100%;
        justify-content: center;
    }

    .trust-logos {
        justify-content: center;
    }

    .security-dashboard {
        max-width: 100%;
    }

    .platform {
        padding: 80px 0;
    }

    .solutions {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .platform-grid {
        grid-template-columns: 1fr;
    }

    .tab-nav {
        flex-direction: column;
    }

    .tab-button {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .nav-container {
        padding: 0 16px;
        height: 70px;
    }

    .brand-logo {
        gap: 8px;
    }

    .brand-icon {
        width: 28px;
        height: 28px;
    }

    .brand-text {
        font-size: 18px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 18px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .platform-card {
        padding: 24px;
    }

    .card-metrics {
        gap: 20px;
    }

    .solution-text h3 {
        font-size: 24px;
    }

    .enterprise-diagram {
        gap: 16px;
    }

    .diagram-node {
        min-width: 160px;
        padding: 20px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Tools Section */
.tools {
    padding: var(--section-padding) 0;
    background: transparent;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
}

.tool-category {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 32px;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
}

.tool-category:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--gray-300);
}

.category-header {
    text-align: center;
    margin-bottom: 24px;
}

.category-icon {
    width: 64px;
    height: 64px;
    background: var(--primary-black);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    margin: 0 auto 16px;
}

.category-header h3 {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-black);
    margin: 0;
}

.tools-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.tool-item {
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    backdrop-filter: blur(12px);
}

.tool-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(0, 0, 0, 0.3);
    transform: translateX(4px);
}

.tool-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-black);
    display: block;
    margin-bottom: 4px;
}

.tool-description {
    font-size: 12px;
    color: var(--gray-600);
    line-height: 1.4;
}

/* Enterprise Section */
.enterprise {
    padding: var(--section-padding) 0;
    background: transparent;
}

.enterprise-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.enterprise-features {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin: 40px 0;
}

.enterprise-feature {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-black);
    flex-shrink: 0;
    backdrop-filter: blur(12px);
}

.feature-content h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: 8px;
}

.feature-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

.enterprise-actions {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.enterprise-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.enterprise-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
    max-width: 400px;
}

.enterprise-stat {
    text-align: center;
    padding: 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
}

.enterprise-stat:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-black);
}

.enterprise-stat .stat-number {
    font-size: 28px;
    font-weight: 800;
    color: var(--primary-black);
    display: block;
    line-height: 1;
    margin-bottom: 8px;
}

.enterprise-stat .stat-label {
    font-size: 12px;
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Contact Section */
.contact {
    padding: var(--section-padding) 0;
    background: transparent;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: flex-start;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 40px;
}

.contact-method {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.method-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-black);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    flex-shrink: 0;
}

.method-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-black);
    margin-bottom: 4px;
}

.method-content p {
    color: var(--gray-600);
    margin: 0;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(20px);
}

.form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-black);
}

.form-group input,
.form-group textarea {
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    font-size: 14px;
    font-family: var(--font-family);
    transition: border-color 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    color: var(--primary-black);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-black);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Footer */
.footer {
    background: var(--primary-black);
    color: var(--primary-white);
    padding: 80px 0 40px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 80px;
    margin-bottom: 40px;
}

.footer-brand .brand-logo {
    color: var(--primary-white);
    margin-bottom: 16px;
}

.footer-brand .brand-icon {
    background: var(--primary-white);
    color: var(--primary-black);
}

.footer-description {
    color: var(--gray-400);
    line-height: 1.6;
    max-width: 300px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.footer-column h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-white);
    margin-bottom: 16px;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-column a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: var(--primary-white);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 40px;
    border-top: 1px solid var(--gray-800);
}

.footer-bottom p {
    color: var(--gray-400);
    margin: 0;
    font-size: 14px;
}

.footer-social {
    display: flex;
    gap: 16px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-white);
    color: var(--primary-black);
}

/* Additional Responsive Design */
@media (max-width: 1024px) {
    .enterprise-content {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
    }

    .enterprise-features {
        gap: 24px;
    }

    .enterprise-feature {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .enterprise-stats {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .enterprise-actions {
        flex-direction: column;
        align-items: center;
        width: 100%;
    }

    .enterprise-actions .btn-large {
        width: 100%;
        justify-content: center;
    }

    .contact-methods {
        gap: 20px;
    }

    .contact-form {
        padding: 24px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}

/* Smooth scrolling */
html {
    scroll-padding-top: 80px;
}
