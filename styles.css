/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Courier New', monospace;
    background: #050505;
    color: #00ff41;
    overflow-x: hidden;
    min-height: 100vh;
    background-image: 
        radial-gradient(circle at 25% 25%, #003d10 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, #001a33 0%, transparent 50%);
}

/* Background Canvas */
#background-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.3;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Main Container */
.main-container {
    position: relative;
    z-index: 1;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

#hero-3d {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-overlay {
    position: relative;
    z-index: 10;
    text-align: center;
    pointer-events: none;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 900;
    color: #00ff41;
    text-shadow: 
        0 0 5px #00ff41,
        0 0 10px #00ff41,
        0 0 15px #00ff41,
        0 0 20px #00ff41;
    margin-bottom: 1rem;
    animation: glow 2s ease-in-out infinite alternate;
}

.hero-subtitle {
    font-size: clamp(1rem, 3vw, 2rem);
    color: #0066ff;
    margin-bottom: 2rem;
    font-weight: 600;
}

.hero-tagline {
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    color: #00ffff;
    letter-spacing: 2px;
}

.scan-line {
    position: absolute;
    top: 33%;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #00ff41 50%, transparent 100%);
    animation: scan 2s linear infinite;
}

/* Animations */
@keyframes glow {
    0% { 
        text-shadow: 
            0 0 5px #00ff41,
            0 0 10px #00ff41,
            0 0 15px #00ff41;
    }
    100% { 
        text-shadow: 
            0 0 10px #00ff41,
            0 0 20px #00ff41,
            0 0 30px #00ff41,
            0 0 40px #00ff41;
    }
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100vw); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Stats Section */
.stats-section {
    padding: 5rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
}

.stat-card:hover {
    border-color: #00ff41;
    box-shadow: 
        0 0 20px rgba(0, 255, 65, 0.4),
        inset 0 0 20px rgba(0, 255, 65, 0.1);
    transform: translateY(-5px);
}

.stat-card:nth-child(2) {
    animation-delay: -2s;
}

.stat-card:nth-child(3) {
    animation-delay: -4s;
}

.stat-icon {
    font-size: 3rem;
    color: #00ff41;
    margin-bottom: 1rem;
    filter: drop-shadow(0 0 10px #00ff41);
}

.stat-value {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 700;
    color: #00ff41;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #0066ff;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 0.9rem;
}

/* Tools Section */
.tools-section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 700;
    color: #00ff41;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px #00ff41;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #0066ff;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Filter */
.filter-container {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.cyber-select {
    background: linear-gradient(45deg, transparent, rgba(0, 255, 65, 0.1), transparent);
    border: 1px solid #00ff41;
    color: #00ff41;
    padding: 12px 24px;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300ff41' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.cyber-select:hover {
    box-shadow: 
        0 0 20px rgba(0, 255, 65, 0.5),
        inset 0 0 20px rgba(0, 255, 65, 0.1);
    text-shadow: 0 0 10px #00ff41;
}

.cyber-select option {
    background: #0a0a0a;
    color: #00ff41;
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.tool-card {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 255, 65, 0.3);
    border-radius: 10px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.tool-card:hover {
    border-color: #00ff41;
    box-shadow: 
        0 0 20px rgba(0, 255, 65, 0.4),
        inset 0 0 20px rgba(0, 255, 65, 0.1);
    transform: translateY(-5px);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.tool-status {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-active {
    color: #00ff41;
    border: 1px solid #00ff41;
}

.status-beta {
    color: #ffaa00;
    border: 1px solid #ffaa00;
}

.status-coming-soon {
    color: #888;
    border: 1px solid #888;
}

.complexity-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.complexity-beginner { background: #00ff00; }
.complexity-intermediate { background: #ffaa00; }
.complexity-advanced { background: #ff6600; }
.complexity-expert { background: #ff0000; }

.tool-icon {
    text-align: center;
    margin-bottom: 1.5rem;
}

.tool-icon-wrapper {
    display: inline-block;
    padding: 1rem;
    border: 2px solid;
    border-radius: 50%;
    font-size: 2.5rem;
    filter: drop-shadow(0 0 10px currentColor);
}

.tool-content {
    text-align: center;
    margin-bottom: 1.5rem;
}

.tool-name {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    font-weight: 700;
    color: #00ff41;
    margin-bottom: 0.5rem;
}

.tool-category {
    color: #0066ff;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tool-description {
    color: #ccc;
    font-size: 0.9rem;
    line-height: 1.5;
}

.tool-features {
    margin-bottom: 1.5rem;
}

.features-title {
    color: #00ff41;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.8rem;
}

.features-list {
    list-style: none;
}

.features-list li {
    color: #aaa;
    font-size: 0.8rem;
    margin-bottom: 0.4rem;
    display: flex;
    align-items: center;
}

.features-list li::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.tool-button {
    width: 100%;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 65, 0.1), transparent);
    border: 1px solid #00ff41;
    color: #00ff41;
    padding: 12px 24px;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-button:hover:not(:disabled) {
    box-shadow: 
        0 0 20px rgba(0, 255, 65, 0.5),
        inset 0 0 20px rgba(0, 255, 65, 0.1);
    text-shadow: 0 0 10px #00ff41;
}

.tool-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Footer */
.footer {
    padding: 3rem 0;
    border-top: 1px solid rgba(0, 255, 65, 0.2);
    text-align: center;
}

.footer-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: #00ff41;
    margin-bottom: 1rem;
}

.footer-subtitle {
    color: #0066ff;
    margin-bottom: 1.5rem;
}

.footer-disclaimer {
    color: #888;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
}
