// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeLucideIcons();
    initializeNavigation();
    initializeTerminalAnimation();
    initializeScrollAnimations();
    initializeChartAnimations();
    initialize3DElements();
});

// Initialize Lucide Icons
function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Navigation functionality
function initializeNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Navbar background on scroll
    const navbar = document.querySelector('.navbar');
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        } else {
            navbar.style.background = 'rgba(10, 10, 10, 0.9)';
        }
    });
}

// Terminal animation
function initializeTerminalAnimation() {
    const terminalLines = document.querySelectorAll('.terminal-line');
    
    // Hide all lines initially except the first one
    terminalLines.forEach((line, index) => {
        if (index > 0) {
            line.style.opacity = '0';
        }
    });
    
    // Animate lines appearing one by one
    let currentLine = 1;
    const animateNextLine = () => {
        if (currentLine < terminalLines.length) {
            terminalLines[currentLine].style.opacity = '1';
            terminalLines[currentLine].style.animation = 'fadeInUp 0.5s ease forwards';
            currentLine++;
            setTimeout(animateNextLine, 800);
        }
    };
    
    // Start animation when hero section is visible
    const hero = document.querySelector('.hero');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                setTimeout(animateNextLine, 1000);
                observer.unobserve(entry.target);
            }
        });
    });
    
    if (hero) {
        observer.observe(hero);
    }
}

// Scroll animations
function initializeScrollAnimations() {
    const animateOnScroll = (entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    };
    
    const scrollObserver = new IntersectionObserver(animateOnScroll, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    // Animate platform cards
    const platformCards = document.querySelectorAll('.platform-card');
    platformCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = `${index * 0.1}s`;
        scrollObserver.observe(card);
    });
    
    // Animate capability items
    const capabilityItems = document.querySelectorAll('.capability-item');
    capabilityItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        item.style.transitionDelay = `${index * 0.2}s`;
        scrollObserver.observe(item);
    });
    
    // Animate section headers
    const sectionHeaders = document.querySelectorAll('.section-header');
    sectionHeaders.forEach(header => {
        header.style.opacity = '0';
        header.style.transform = 'translateY(30px)';
        header.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        scrollObserver.observe(header);
    });

    // Animate tool categories
    const toolCategories = document.querySelectorAll('.tool-category');
    toolCategories.forEach((category, index) => {
        category.style.opacity = '0';
        category.style.transform = 'translateY(50px)';
        category.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        category.style.transitionDelay = `${index * 0.2}s`;
        scrollObserver.observe(category);
    });

    // Animate tool items
    const toolItems = document.querySelectorAll('.tool-item');
    toolItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px) rotateX(-10deg)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        item.style.transitionDelay = `${(index % 5) * 0.1}s`;
        scrollObserver.observe(item);
    });
}

// Chart animations
function initializeChartAnimations() {
    const chartBars = document.querySelectorAll('.bar-fill');
    
    const animateChart = (entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                chartBars.forEach((bar, index) => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, index * 200);
                });
                observer.unobserve(entry.target);
            }
        });
    };
    
    const chartObserver = new IntersectionObserver(animateChart, {
        threshold: 0.5
    });
    
    const benchmarkChart = document.querySelector('.benchmark-chart');
    if (benchmarkChart) {
        chartObserver.observe(benchmarkChart);
    }
}

// Button interactions
document.addEventListener('click', (e) => {
    if (e.target.matches('.btn-primary') || e.target.closest('.btn-primary')) {
        const button = e.target.matches('.btn-primary') ? e.target : e.target.closest('.btn-primary');
        
        // Add click animation
        button.style.transform = 'translateY(-2px) scale(0.98)';
        setTimeout(() => {
            button.style.transform = 'translateY(-2px) scale(1)';
        }, 150);
        
        // Handle different button actions
        const buttonText = button.textContent.trim();
        
        if (buttonText.includes('Start Free Trial') || buttonText.includes('Get Started')) {
            // Simulate trial signup
            showNotification('Free trial started! Check your email for setup instructions.', 'success');
        } else if (buttonText.includes('Book Demo') || buttonText.includes('Contact Sales')) {
            // Simulate demo booking
            showNotification('Demo request submitted! Our team will contact you within 24 hours.', 'info');
        }
    }
});

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}"></i>
            <span>${message}</span>
            <button class="notification-close">
                <i data-lucide="x"></i>
            </button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 24px;
        z-index: 10000;
        background: ${type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        transform: translateX(400px);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;
    
    const notificationContent = notification.querySelector('.notification-content');
    notificationContent.style.cssText = `
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        font-weight: 500;
    `;
    
    const closeButton = notification.querySelector('.notification-close');
    closeButton.style.cssText = `
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    const autoRemove = setTimeout(() => {
        removeNotification(notification);
    }, 5000);
    
    // Manual close
    closeButton.addEventListener('click', () => {
        clearTimeout(autoRemove);
        removeNotification(notification);
    });
}

function removeNotification(notification) {
    notification.style.transform = 'translateX(400px)';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Parallax effect for hero background
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    
    if (hero && scrolled < window.innerHeight) {
        const rate = scrolled * -0.5;
        hero.style.transform = `translateY(${rate}px)`;
    }
});

// Performance optimization: Debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll events
const debouncedScrollHandler = debounce(() => {
    // Any scroll-based animations or effects can go here
}, 16); // ~60fps

window.addEventListener('scroll', debouncedScrollHandler);

// 3D Elements Initialization
function initialize3DElements() {
    if (typeof THREE === 'undefined') {
        console.warn('Three.js not loaded');
        return;
    }

    initializeBackground3D();
    initializeHero3D();
    initializeCard3D();
}

// 3D Background
function initializeBackground3D() {
    const container = document.getElementById('background-3d');
    if (!container) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // Create subtle floating particles
    const particleGeometry = new THREE.BufferGeometry();
    const particleCount = 25; // Further reduced count
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
        positions[i] = (Math.random() - 0.5) * 120;
        positions[i + 1] = (Math.random() - 0.5) * 120;
        positions[i + 2] = (Math.random() - 0.5) * 120;

        // Cyberpunk colors - Matrix green theme
        const colorType = Math.floor(Math.random() * 3);
        if (colorType === 0) {
            // Matrix green
            colors[i] = 0;
            colors[i + 1] = 1;
            colors[i + 2] = 0.3;
        } else if (colorType === 1) {
            // Dark green
            colors[i] = 0;
            colors[i + 1] = 0.8;
            colors[i + 2] = 0.2;
        } else {
            // Light green
            colors[i] = 0.2;
            colors[i + 1] = 1;
            colors[i + 2] = 0.4;
        }
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const particleMaterial = new THREE.PointsMaterial({
        size: 1, // Even smaller size
        vertexColors: true,
        transparent: true,
        opacity: 0.2 // Much more subtle
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Create improved neural network
    const networkGeometry = new THREE.BufferGeometry();
    const networkPositions = [];
    const networkColors = [];

    // Create simplified neural network
    const nodes = [];
    const layers = [4, 6, 4]; // Reduced layers and nodes
    let totalNodes = 0;

    // Generate nodes in structured layers
    for (let layerIndex = 0; layerIndex < layers.length; layerIndex++) {
        const nodesInLayer = layers[layerIndex];
        const x = (layerIndex - layers.length/2) * 20;

        for (let nodeIndex = 0; nodeIndex < nodesInLayer; nodeIndex++) {
            const y = (nodeIndex - nodesInLayer/2) * 8;
            const z = (Math.random() - 0.5) * 15;

            nodes.push({
                x, y, z,
                layer: layerIndex,
                nodeIndex,
                id: totalNodes++,
                activation: Math.random()
            });
        }
    }

    // Create smart connections (only between adjacent layers)
    for (let i = 0; i < nodes.length; i++) {
        const nodeA = nodes[i];

        for (let j = 0; j < nodes.length; j++) {
            const nodeB = nodes[j];

            // Only connect nodes in adjacent layers
            if (Math.abs(nodeA.layer - nodeB.layer) === 1) {
                // Reduce connections for better performance
                if (Math.random() > 0.6) {
                    networkPositions.push(nodeA.x, nodeA.y, nodeA.z);
                    networkPositions.push(nodeB.x, nodeB.y, nodeB.z);

                    // Color based on connection strength
                    const strength = (nodeA.activation + nodeB.activation) / 2;
                    networkColors.push(0, strength, 0.2);
                    networkColors.push(0, strength * 0.8, 0.15);
                }
            }
        }
    }

    networkGeometry.setAttribute('position', new THREE.Float32BufferAttribute(networkPositions, 3));
    networkGeometry.setAttribute('color', new THREE.Float32BufferAttribute(networkColors, 3));

    const networkMaterial = new THREE.LineBasicMaterial({
        vertexColors: true,
        transparent: true,
        opacity: 0.2,
        linewidth: 1
    });

    const network = new THREE.LineSegments(networkGeometry, networkMaterial);
    scene.add(network);

    // Create neural nodes with different sizes based on layer
    const nodeGeometry = new THREE.BufferGeometry();
    const nodePositions = [];
    const nodeColors = [];
    const nodeSizes = [];

    nodes.forEach(node => {
        nodePositions.push(node.x, node.y, node.z);

        // Color based on activation level
        const activation = node.activation;
        nodeColors.push(0, activation, 0.3);

        // Size based on layer (middle layers are bigger)
        const layerSize = layers[node.layer];
        const sizeMultiplier = 1 + (layerSize / 10);
        nodeSizes.push(sizeMultiplier);
    });

    nodeGeometry.setAttribute('position', new THREE.Float32BufferAttribute(nodePositions, 3));
    nodeGeometry.setAttribute('color', new THREE.Float32BufferAttribute(nodeColors, 3));
    nodeGeometry.setAttribute('size', new THREE.Float32BufferAttribute(nodeSizes, 1));

    const nodeMaterial = new THREE.PointsMaterial({
        size: 2,
        vertexColors: true,
        transparent: true,
        opacity: 0.4,
        sizeAttenuation: true
    });

    const networkNodes = new THREE.Points(nodeGeometry, nodeMaterial);
    scene.add(networkNodes);

    // Add data flow animation
    const flowGeometry = new THREE.BufferGeometry();
    const flowPositions = [];
    const flowColors = [];

    // Create flowing data particles (reduced)
    for (let i = 0; i < 8; i++) {
        const startLayer = Math.floor(Math.random() * (layers.length - 1));
        const endLayer = startLayer + 1;

        const startNodes = nodes.filter(n => n.layer === startLayer);
        const endNodes = nodes.filter(n => n.layer === endLayer);

        if (startNodes.length > 0 && endNodes.length > 0) {
            const startNode = startNodes[Math.floor(Math.random() * startNodes.length)];
            const endNode = endNodes[Math.floor(Math.random() * endNodes.length)];

            // Create path between nodes
            for (let t = 0; t <= 1; t += 0.1) {
                const x = startNode.x + (endNode.x - startNode.x) * t;
                const y = startNode.y + (endNode.y - startNode.y) * t;
                const z = startNode.z + (endNode.z - startNode.z) * t;

                flowPositions.push(x, y, z);
                flowColors.push(0, 1, 0.5);
            }
        }
    }

    flowGeometry.setAttribute('position', new THREE.Float32BufferAttribute(flowPositions, 3));
    flowGeometry.setAttribute('color', new THREE.Float32BufferAttribute(flowColors, 3));

    const flowMaterial = new THREE.PointsMaterial({
        size: 1,
        vertexColors: true,
        transparent: true,
        opacity: 0.3
    });

    const dataFlow = new THREE.Points(flowGeometry, flowMaterial);
    scene.add(dataFlow);

    camera.position.z = 50;

    // Animation
    function animate() {
        requestAnimationFrame(animate);

        particles.rotation.x += 0.0005;
        particles.rotation.y += 0.001;

        // Subtle neural network animation
        network.rotation.y += 0.0005;

        // Gentle pulsing effect for neural connections
        const time = Date.now() * 0.001;
        networkMaterial.opacity = 0.15 + Math.sin(time * 0.8) * 0.1;

        // Subtle animation for neural nodes
        networkNodes.rotation.y += 0.0003;
        nodeMaterial.opacity = 0.3 + Math.sin(time * 1.2) * 0.1;

        // Gentle data flow animation
        dataFlow.rotation.y += 0.001;
        flowMaterial.opacity = 0.2 + Math.sin(time * 1.5) * 0.2;

        // Update node activations for dynamic effect
        const nodeColors = networkNodes.geometry.attributes.color.array;

        for (let i = 0; i < nodeColors.length; i += 3) {
            const activation = 0.3 + Math.sin(time * 3 + i) * 0.7;
            nodeColors[i + 1] = activation; // Green component
        }
        networkNodes.geometry.attributes.color.needsUpdate = true;

        renderer.render(scene, camera);
    }

    animate();

    // Handle resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
}

// Hero 3D Scene
function initializeHero3D() {
    const container = document.getElementById('hero-3d-scene');
    if (!container) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

    renderer.setSize(300, 300);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // Create a rotating torus with cyber colors
    const torusGeometry = new THREE.TorusGeometry(1.5, 0.3, 16, 100);
    const torusMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ff41,
        wireframe: true,
        transparent: true,
        opacity: 0.8
    });
    const torus = new THREE.Mesh(torusGeometry, torusMaterial);
    scene.add(torus);

    // Create elegant floating rings
    const rings = [];
    for (let i = 0; i < 3; i++) {
        const ringGeometry = new THREE.TorusGeometry(0.8 + i * 0.3, 0.05, 8, 32);
        const ringMaterial = new THREE.MeshBasicMaterial({
            color: [0x00ff41, 0x00cc33, 0x009926][i],
            transparent: true,
            opacity: 0.7 - i * 0.1
        });
        const ring = new THREE.Mesh(ringGeometry, ringMaterial);

        ring.position.set(0, 0, 0);
        ring.rotation.x = Math.PI / 4 + i * 0.2;
        ring.rotation.y = i * 0.3;

        scene.add(ring);
        rings.push(ring);
    }

    camera.position.z = 5;

    // Animation
    function animate() {
        requestAnimationFrame(animate);

        torus.rotation.x += 0.01;
        torus.rotation.y += 0.02;

        rings.forEach((ring, index) => {
            ring.rotation.x += 0.005 + index * 0.002;
            ring.rotation.y += 0.008 + index * 0.001;
            ring.rotation.z += 0.003 + index * 0.001;
        });

        renderer.render(scene, camera);
    }

    animate();
}

// Card 3D Elements
function initializeCard3D() {
    const cardContainers = document.querySelectorAll('.card-3d-container');

    cardContainers.forEach((container) => {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

        renderer.setSize(100, 100);
        renderer.setClearColor(0x000000, 0);
        container.appendChild(renderer.domElement);

        // Create different 3D objects for each card
        let object;
        const cardType = container.parentElement.getAttribute('data-3d');

        switch(cardType) {
            case 'brain':
                // Neural network visualization
                const sphereGeometry = new THREE.SphereGeometry(0.8, 16, 16);
                const sphereMaterial = new THREE.MeshBasicMaterial({
                    color: 0x3b82f6,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(sphereGeometry, sphereMaterial);
                break;

            case 'lightning':
                // Lightning bolt shape
                const coneGeometry = new THREE.ConeGeometry(0.3, 1.5, 8);
                const coneMaterial = new THREE.MeshBasicMaterial({
                    color: 0x8b5cf6,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(coneGeometry, coneMaterial);
                break;

            case 'document':
                // Document/report shape
                const boxGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.1);
                const boxMaterial = new THREE.MeshBasicMaterial({
                    color: 0x06b6d4,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(boxGeometry, boxMaterial);
                break;

            case 'shield':
                // Shield shape
                const octahedronGeometry = new THREE.OctahedronGeometry(0.8);
                const octahedronMaterial = new THREE.MeshBasicMaterial({
                    color: 0x10b981,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(octahedronGeometry, octahedronMaterial);
                break;

            case 'network':
                // Network nodes
                const torusGeometry = new THREE.TorusGeometry(0.6, 0.2, 8, 16);
                const torusMaterial = new THREE.MeshBasicMaterial({
                    color: 0xf59e0b,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(torusGeometry, torusMaterial);
                break;

            default:
                // Default growth/trending shape
                const tetrahedronGeometry = new THREE.TetrahedronGeometry(0.8);
                const tetrahedronMaterial = new THREE.MeshBasicMaterial({
                    color: 0xef4444,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(tetrahedronGeometry, tetrahedronMaterial);
        }

        scene.add(object);
        camera.position.z = 2;

        // Animation
        function animate() {
            requestAnimationFrame(animate);

            object.rotation.x += 0.01;
            object.rotation.y += 0.02;

            renderer.render(scene, camera);
        }

        animate();

        // Add hover effect
        const card = container.parentElement;
        card.addEventListener('mouseenter', () => {
            object.scale.set(1.2, 1.2, 1.2);
        });

        card.addEventListener('mouseleave', () => {
            object.scale.set(1, 1, 1);
        });
    });
}

// Mouse interaction for 3D elements
function add3DMouseInteraction() {
    const platformCards = document.querySelectorAll('.platform-card');

    platformCards.forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / centerY * 10;
            const rotateY = (centerX - x) / centerX * 10;

            card.style.transform = `
                translateY(-8px)
                rotateX(${rotateX}deg)
                rotateY(${rotateY}deg)
                scale(1.02)
            `;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) rotateX(0) rotateY(0) scale(1)';
        });
    });
}

// Initialize mouse interactions
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(add3DMouseInteraction, 1000);
    setTimeout(addToolItemInteractions, 1000);
});

// Tool item interactions
function addToolItemInteractions() {
    const toolItems = document.querySelectorAll('.tool-item');

    toolItems.forEach(item => {
        item.addEventListener('click', () => {
            // Add click animation
            item.style.transform = 'translateY(-5px) rotateX(2deg) scale(0.98)';
            setTimeout(() => {
                item.style.transform = 'translateY(-5px) rotateX(2deg) scale(1)';
            }, 150);

            // Show tool info notification
            const toolName = item.querySelector('h4').textContent;
            const toolDesc = item.querySelector('p').textContent;
            showNotification(`${toolName}: ${toolDesc}`, 'info');
        });

        item.addEventListener('mouseenter', () => {
            // Add subtle glow effect
            item.style.boxShadow = `
                0 15px 30px rgba(59, 130, 246, 0.2),
                0 0 0 1px rgba(59, 130, 246, 0.3),
                0 0 50px rgba(59, 130, 246, 0.1)
            `;
        });

        item.addEventListener('mouseleave', () => {
            // Reset glow effect
            item.style.boxShadow = '';
        });
    });
}

// Parallax effect for 3D background
let mouseX = 0;
let mouseY = 0;

document.addEventListener('mousemove', (e) => {
    mouseX = (e.clientX / window.innerWidth) * 2 - 1;
    mouseY = -(e.clientY / window.innerHeight) * 2 + 1;
});

// Add this to the background 3D animation loop
function updateBackgroundParallax(camera) {
    if (camera) {
        camera.position.x += (mouseX * 5 - camera.position.x) * 0.05;
        camera.position.y += (mouseY * 5 - camera.position.y) * 0.05;
    }
}
