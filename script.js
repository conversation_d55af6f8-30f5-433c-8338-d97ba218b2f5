// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeLucideIcons();
    initializeNavigation();
    initializeTerminalAnimation();
    initializeScrollAnimations();
    initializeChartAnimations();
    initialize3DElements();
});

// Initialize Lucide Icons
function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Navigation functionality
function initializeNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Navbar background on scroll
    const navbar = document.querySelector('.navbar');
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        } else {
            navbar.style.background = 'rgba(10, 10, 10, 0.9)';
        }
    });
}

// Terminal animation
function initializeTerminalAnimation() {
    const terminalLines = document.querySelectorAll('.terminal-line');
    
    // Hide all lines initially except the first one
    terminalLines.forEach((line, index) => {
        if (index > 0) {
            line.style.opacity = '0';
        }
    });
    
    // Animate lines appearing one by one
    let currentLine = 1;
    const animateNextLine = () => {
        if (currentLine < terminalLines.length) {
            terminalLines[currentLine].style.opacity = '1';
            terminalLines[currentLine].style.animation = 'fadeInUp 0.5s ease forwards';
            currentLine++;
            setTimeout(animateNextLine, 800);
        }
    };
    
    // Start animation when hero section is visible
    const hero = document.querySelector('.hero');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                setTimeout(animateNextLine, 1000);
                observer.unobserve(entry.target);
            }
        });
    });
    
    if (hero) {
        observer.observe(hero);
    }
}

// Scroll animations
function initializeScrollAnimations() {
    const animateOnScroll = (entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    };
    
    const scrollObserver = new IntersectionObserver(animateOnScroll, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    // Animate platform cards
    const platformCards = document.querySelectorAll('.platform-card');
    platformCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = `${index * 0.1}s`;
        scrollObserver.observe(card);
    });
    
    // Animate capability items
    const capabilityItems = document.querySelectorAll('.capability-item');
    capabilityItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        item.style.transitionDelay = `${index * 0.2}s`;
        scrollObserver.observe(item);
    });
    
    // Animate section headers
    const sectionHeaders = document.querySelectorAll('.section-header');
    sectionHeaders.forEach(header => {
        header.style.opacity = '0';
        header.style.transform = 'translateY(30px)';
        header.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        scrollObserver.observe(header);
    });
}

// Chart animations
function initializeChartAnimations() {
    const chartBars = document.querySelectorAll('.bar-fill');
    
    const animateChart = (entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                chartBars.forEach((bar, index) => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, index * 200);
                });
                observer.unobserve(entry.target);
            }
        });
    };
    
    const chartObserver = new IntersectionObserver(animateChart, {
        threshold: 0.5
    });
    
    const benchmarkChart = document.querySelector('.benchmark-chart');
    if (benchmarkChart) {
        chartObserver.observe(benchmarkChart);
    }
}

// Button interactions
document.addEventListener('click', (e) => {
    if (e.target.matches('.btn-primary') || e.target.closest('.btn-primary')) {
        const button = e.target.matches('.btn-primary') ? e.target : e.target.closest('.btn-primary');
        
        // Add click animation
        button.style.transform = 'translateY(-2px) scale(0.98)';
        setTimeout(() => {
            button.style.transform = 'translateY(-2px) scale(1)';
        }, 150);
        
        // Handle different button actions
        const buttonText = button.textContent.trim();
        
        if (buttonText.includes('Start Free Trial') || buttonText.includes('Get Started')) {
            // Simulate trial signup
            showNotification('Free trial started! Check your email for setup instructions.', 'success');
        } else if (buttonText.includes('Book Demo') || buttonText.includes('Contact Sales')) {
            // Simulate demo booking
            showNotification('Demo request submitted! Our team will contact you within 24 hours.', 'info');
        }
    }
});

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}"></i>
            <span>${message}</span>
            <button class="notification-close">
                <i data-lucide="x"></i>
            </button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 24px;
        z-index: 10000;
        background: ${type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        transform: translateX(400px);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;
    
    const notificationContent = notification.querySelector('.notification-content');
    notificationContent.style.cssText = `
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        font-weight: 500;
    `;
    
    const closeButton = notification.querySelector('.notification-close');
    closeButton.style.cssText = `
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    const autoRemove = setTimeout(() => {
        removeNotification(notification);
    }, 5000);
    
    // Manual close
    closeButton.addEventListener('click', () => {
        clearTimeout(autoRemove);
        removeNotification(notification);
    });
}

function removeNotification(notification) {
    notification.style.transform = 'translateX(400px)';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Parallax effect for hero background
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    
    if (hero && scrolled < window.innerHeight) {
        const rate = scrolled * -0.5;
        hero.style.transform = `translateY(${rate}px)`;
    }
});

// Performance optimization: Debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll events
const debouncedScrollHandler = debounce(() => {
    // Any scroll-based animations or effects can go here
}, 16); // ~60fps

window.addEventListener('scroll', debouncedScrollHandler);

// 3D Elements Initialization
function initialize3DElements() {
    if (typeof THREE === 'undefined') {
        console.warn('Three.js not loaded');
        return;
    }

    initializeBackground3D();
    initializeHero3D();
    initializeCard3D();
}

// 3D Background
function initializeBackground3D() {
    const container = document.getElementById('background-3d');
    if (!container) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // Create floating particles
    const particleGeometry = new THREE.BufferGeometry();
    const particleCount = 100;
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
        positions[i] = (Math.random() - 0.5) * 100;
        positions[i + 1] = (Math.random() - 0.5) * 100;
        positions[i + 2] = (Math.random() - 0.5) * 100;

        // Blue to purple gradient colors
        colors[i] = 0.2 + Math.random() * 0.3;     // R
        colors[i + 1] = 0.5 + Math.random() * 0.3; // G
        colors[i + 2] = 0.8 + Math.random() * 0.2; // B
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const particleMaterial = new THREE.PointsMaterial({
        size: 2,
        vertexColors: true,
        transparent: true,
        opacity: 0.6
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Create connecting lines
    const lineGeometry = new THREE.BufferGeometry();
    const linePositions = [];

    for (let i = 0; i < 50; i++) {
        linePositions.push(
            (Math.random() - 0.5) * 80,
            (Math.random() - 0.5) * 80,
            (Math.random() - 0.5) * 80,
            (Math.random() - 0.5) * 80,
            (Math.random() - 0.5) * 80,
            (Math.random() - 0.5) * 80
        );
    }

    lineGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linePositions, 3));

    const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x3b82f6,
        transparent: true,
        opacity: 0.2
    });

    const lines = new THREE.LineSegments(lineGeometry, lineMaterial);
    scene.add(lines);

    camera.position.z = 50;

    // Animation
    function animate() {
        requestAnimationFrame(animate);

        particles.rotation.x += 0.001;
        particles.rotation.y += 0.002;

        lines.rotation.x += 0.0005;
        lines.rotation.y += 0.001;

        renderer.render(scene, camera);
    }

    animate();

    // Handle resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
}

// Hero 3D Scene
function initializeHero3D() {
    const container = document.getElementById('hero-3d-scene');
    if (!container) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

    renderer.setSize(300, 300);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // Create a rotating torus
    const torusGeometry = new THREE.TorusGeometry(1.5, 0.3, 16, 100);
    const torusMaterial = new THREE.MeshBasicMaterial({
        color: 0x3b82f6,
        wireframe: true,
        transparent: true,
        opacity: 0.8
    });
    const torus = new THREE.Mesh(torusGeometry, torusMaterial);
    scene.add(torus);

    // Create floating cubes
    const cubes = [];
    for (let i = 0; i < 5; i++) {
        const cubeGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
        const cubeMaterial = new THREE.MeshBasicMaterial({
            color: [0x3b82f6, 0x8b5cf6, 0x06b6d4][i % 3],
            transparent: true,
            opacity: 0.7
        });
        const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);

        cube.position.x = (Math.random() - 0.5) * 4;
        cube.position.y = (Math.random() - 0.5) * 4;
        cube.position.z = (Math.random() - 0.5) * 4;

        scene.add(cube);
        cubes.push(cube);
    }

    camera.position.z = 5;

    // Animation
    function animate() {
        requestAnimationFrame(animate);

        torus.rotation.x += 0.01;
        torus.rotation.y += 0.02;

        cubes.forEach((cube, index) => {
            cube.rotation.x += 0.01 + index * 0.002;
            cube.rotation.y += 0.01 + index * 0.002;
            cube.position.y += Math.sin(Date.now() * 0.001 + index) * 0.002;
        });

        renderer.render(scene, camera);
    }

    animate();
}

// Card 3D Elements
function initializeCard3D() {
    const cardContainers = document.querySelectorAll('.card-3d-container');

    cardContainers.forEach((container) => {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });

        renderer.setSize(100, 100);
        renderer.setClearColor(0x000000, 0);
        container.appendChild(renderer.domElement);

        // Create different 3D objects for each card
        let object;
        const cardType = container.parentElement.getAttribute('data-3d');

        switch(cardType) {
            case 'brain':
                // Neural network visualization
                const sphereGeometry = new THREE.SphereGeometry(0.8, 16, 16);
                const sphereMaterial = new THREE.MeshBasicMaterial({
                    color: 0x3b82f6,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(sphereGeometry, sphereMaterial);
                break;

            case 'lightning':
                // Lightning bolt shape
                const coneGeometry = new THREE.ConeGeometry(0.3, 1.5, 8);
                const coneMaterial = new THREE.MeshBasicMaterial({
                    color: 0x8b5cf6,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(coneGeometry, coneMaterial);
                break;

            case 'document':
                // Document/report shape
                const boxGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.1);
                const boxMaterial = new THREE.MeshBasicMaterial({
                    color: 0x06b6d4,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(boxGeometry, boxMaterial);
                break;

            case 'shield':
                // Shield shape
                const octahedronGeometry = new THREE.OctahedronGeometry(0.8);
                const octahedronMaterial = new THREE.MeshBasicMaterial({
                    color: 0x10b981,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(octahedronGeometry, octahedronMaterial);
                break;

            case 'network':
                // Network nodes
                const torusGeometry = new THREE.TorusGeometry(0.6, 0.2, 8, 16);
                const torusMaterial = new THREE.MeshBasicMaterial({
                    color: 0xf59e0b,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(torusGeometry, torusMaterial);
                break;

            default:
                // Default growth/trending shape
                const tetrahedronGeometry = new THREE.TetrahedronGeometry(0.8);
                const tetrahedronMaterial = new THREE.MeshBasicMaterial({
                    color: 0xef4444,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.8
                });
                object = new THREE.Mesh(tetrahedronGeometry, tetrahedronMaterial);
        }

        scene.add(object);
        camera.position.z = 2;

        // Animation
        function animate() {
            requestAnimationFrame(animate);

            object.rotation.x += 0.01;
            object.rotation.y += 0.02;

            renderer.render(scene, camera);
        }

        animate();

        // Add hover effect
        const card = container.parentElement;
        card.addEventListener('mouseenter', () => {
            object.scale.set(1.2, 1.2, 1.2);
        });

        card.addEventListener('mouseleave', () => {
            object.scale.set(1, 1, 1);
        });
    });
}

// Mouse interaction for 3D elements
function add3DMouseInteraction() {
    const platformCards = document.querySelectorAll('.platform-card');

    platformCards.forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / centerY * 10;
            const rotateY = (centerX - x) / centerX * 10;

            card.style.transform = `
                translateY(-8px)
                rotateX(${rotateX}deg)
                rotateY(${rotateY}deg)
                scale(1.02)
            `;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) rotateX(0) rotateY(0) scale(1)';
        });
    });
}

// Initialize mouse interactions
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(add3DMouseInteraction, 1000);
});

// Parallax effect for 3D background
let mouseX = 0;
let mouseY = 0;

document.addEventListener('mousemove', (e) => {
    mouseX = (e.clientX / window.innerWidth) * 2 - 1;
    mouseY = -(e.clientY / window.innerHeight) * 2 + 1;
});

// Add this to the background 3D animation loop
function updateBackgroundParallax(camera) {
    if (camera) {
        camera.position.x += (mouseX * 5 - camera.position.x) * 0.05;
        camera.position.y += (mouseY * 5 - camera.position.y) * 0.05;
    }
}
