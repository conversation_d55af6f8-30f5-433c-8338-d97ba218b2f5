// Tools Data
const tools = [
    {
        id: 'network-scanner',
        name: 'Network Scanner',
        category: 'network',
        description: 'Advanced network discovery and port scanning tool with stealth capabilities',
        features: [
            'TCP/UDP Port Scanning',
            'Service Detection',
            'OS Fingerprinting',
            'Stealth Scan Modes',
            'Network Topology Mapping',
            'Real-time Monitoring'
        ],
        icon: 'network',
        color: '#00ff41',
        status: 'active',
        complexity: 'intermediate'
    },
    {
        id: 'vulnerability-scanner',
        name: 'Vulnerability Scanner',
        category: 'security',
        description: 'Comprehensive vulnerability assessment with AI-powered threat detection',
        features: [
            'CVE Database Integration',
            'Zero-day Detection',
            'Risk Assessment',
            'Automated Reporting',
            'Compliance Checking',
            'Patch Management'
        ],
        icon: 'shield-alert',
        color: '#ff0066',
        status: 'active',
        complexity: 'advanced'
    },
    {
        id: 'password-cracker',
        name: 'Password Cracker',
        category: 'auth',
        description: 'High-performance password recovery and strength testing suite',
        features: [
            'Dictionary Attacks',
            'Brute Force Methods',
            'Rainbow Tables',
            'Hash Analysis',
            'GPU Acceleration',
            'Custom Wordlists'
        ],
        icon: 'key',
        color: '#0066ff',
        status: 'active',
        complexity: 'expert'
    },
    {
        id: 'web-app-tester',
        name: 'Web Application Tester',
        category: 'web',
        description: 'Automated web application security testing with OWASP compliance',
        features: [
            'SQL Injection Detection',
            'XSS Vulnerability Scanning',
            'CSRF Testing',
            'Authentication Bypass',
            'API Security Testing',
            'OWASP Top 10 Coverage'
        ],
        icon: 'globe',
        color: '#00ffff',
        status: 'active',
        complexity: 'intermediate'
    },
    {
        id: 'social-engineering',
        name: 'Social Engineering Toolkit',
        category: 'social',
        description: 'Comprehensive social engineering assessment and awareness training',
        features: [
            'Phishing Campaigns',
            'Email Templates',
            'Social Media OSINT',
            'Awareness Training',
            'Reporting Dashboard',
            'Compliance Tracking'
        ],
        icon: 'users',
        color: '#ff6600',
        status: 'beta',
        complexity: 'advanced'
    },
    {
        id: 'forensics-analyzer',
        name: 'Digital Forensics Analyzer',
        category: 'forensics',
        description: 'Advanced digital forensics and incident response capabilities',
        features: [
            'Memory Analysis',
            'Disk Imaging',
            'Network Forensics',
            'Timeline Analysis',
            'Evidence Chain',
            'Report Generation'
        ],
        icon: 'search',
        color: '#9900ff',
        status: 'active',
        complexity: 'expert'
    },
    {
        id: 'wireless-auditor',
        name: 'Wireless Security Auditor',
        category: 'wireless',
        description: 'Comprehensive wireless network security assessment tool',
        features: [
            'WiFi Network Discovery',
            'WPA/WPA2 Testing',
            'Bluetooth Analysis',
            'Rogue AP Detection',
            'Signal Analysis',
            'Encryption Testing'
        ],
        icon: 'wifi',
        color: '#ffff00',
        status: 'coming-soon',
        complexity: 'advanced'
    },
    {
        id: 'malware-analyzer',
        name: 'Malware Analyzer',
        category: 'malware',
        description: 'Advanced malware detection and behavioral analysis platform',
        features: [
            'Static Analysis',
            'Dynamic Analysis',
            'Sandbox Environment',
            'Threat Intelligence',
            'IOC Extraction',
            'Family Classification'
        ],
        icon: 'bug',
        color: '#ff3300',
        status: 'beta',
        complexity: 'expert'
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeBackground();
    initializeHero3D();
    initializeLucideIcons();
    renderTools();
    initializeFilters();
});

// Background Matrix Effect
function initializeBackground() {
    const canvas = document.getElementById('background-canvas');
    const ctx = canvas.getContext('2d');
    
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()_+-=[]{}|;:,.<>?';
    const charArray = chars.split('');
    const fontSize = 14;
    const columns = canvas.width / fontSize;
    const drops = [];
    
    for (let i = 0; i < columns; i++) {
        drops[i] = 1;
    }
    
    function draw() {
        ctx.fillStyle = 'rgba(5, 5, 5, 0.05)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.fillStyle = '#00ff41';
        ctx.font = `${fontSize}px monospace`;
        
        for (let i = 0; i < drops.length; i++) {
            const text = charArray[Math.floor(Math.random() * charArray.length)];
            ctx.fillText(text, i * fontSize, drops[i] * fontSize);
            
            if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                drops[i] = 0;
            }
            drops[i]++;
        }
    }
    
    setInterval(draw, 50);
    
    // Floating particles
    const particles = [];
    const colors = ['#00ff41', '#0066ff', '#ff0066', '#00ffff'];
    
    for (let i = 0; i < 50; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 3 + 1,
            opacity: Math.random() * 0.5 + 0.2,
            color: colors[Math.floor(Math.random() * colors.length)]
        });
    }
    
    function animateParticles() {
        particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            if (particle.x < 0) particle.x = canvas.width;
            if (particle.x > canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = canvas.height;
            if (particle.y > canvas.height) particle.y = 0;
            
            ctx.save();
            ctx.globalAlpha = particle.opacity;
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
            
            ctx.save();
            ctx.globalAlpha = particle.opacity * 0.3;
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size * 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        });
    }
    
    setInterval(animateParticles, 16);
}

// Hero 3D Scene
function initializeHero3D() {
    const container = document.getElementById('hero-3d');
    if (!container || typeof THREE === 'undefined') return;
    
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);
    
    // Create floating cubes
    const cubes = [];
    const cubeColors = [0x00ff41, 0x0066ff, 0xff0066, 0x00ffff];
    
    for (let i = 0; i < 8; i++) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshBasicMaterial({ 
            color: cubeColors[i % cubeColors.length],
            transparent: true,
            opacity: 0.7,
            wireframe: true
        });
        const cube = new THREE.Mesh(geometry, material);
        
        cube.position.x = (Math.random() - 0.5) * 20;
        cube.position.y = (Math.random() - 0.5) * 10;
        cube.position.z = (Math.random() - 0.5) * 20;
        
        scene.add(cube);
        cubes.push(cube);
    }
    
    // Create torus
    const torusGeometry = new THREE.TorusGeometry(3, 0.5, 16, 100);
    const torusMaterial = new THREE.MeshBasicMaterial({ 
        color: 0x0066ff,
        transparent: true,
        opacity: 0.6,
        wireframe: true
    });
    const torus = new THREE.Mesh(torusGeometry, torusMaterial);
    torus.position.z = -5;
    scene.add(torus);
    
    camera.position.z = 10;
    
    // Animation loop
    function animate() {
        requestAnimationFrame(animate);
        
        cubes.forEach((cube, index) => {
            cube.rotation.x += 0.01 + index * 0.001;
            cube.rotation.y += 0.01 + index * 0.001;
            cube.position.y += Math.sin(Date.now() * 0.001 + index) * 0.01;
        });
        
        torus.rotation.x += 0.01;
        torus.rotation.y += 0.02;
        
        renderer.render(scene, camera);
    }
    
    animate();
    
    // Handle resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
}

// Initialize Lucide Icons
function initializeLucideIcons() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Render Tools
function renderTools(filteredTools = tools) {
    const toolsGrid = document.getElementById('tools-grid');
    if (!toolsGrid) return;
    
    toolsGrid.innerHTML = '';
    
    filteredTools.forEach((tool, index) => {
        const toolCard = createToolCard(tool, index);
        toolsGrid.appendChild(toolCard);
    });
    
    // Reinitialize icons for new content
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// Create Tool Card
function createToolCard(tool, index) {
    const card = document.createElement('div');
    card.className = 'tool-card';
    card.style.animationDelay = `${index * 0.1}s`;
    card.style.borderColor = tool.color;
    
    const statusClass = `status-${tool.status.replace('-', '-')}`;
    const complexityClass = `complexity-${tool.complexity}`;
    
    card.innerHTML = `
        <div class="tool-header">
            <div class="tool-status ${statusClass}">
                ${tool.status.replace('-', ' ')}
            </div>
            <div class="complexity-indicator ${complexityClass}" 
                 title="Complexity: ${tool.complexity}">
            </div>
        </div>
        
        <div class="tool-icon">
            <div class="tool-icon-wrapper" style="border-color: ${tool.color}; color: ${tool.color};">
                <i data-lucide="${tool.icon}"></i>
            </div>
        </div>
        
        <div class="tool-content">
            <h3 class="tool-name">${tool.name}</h3>
            <div class="tool-category">${getCategoryName(tool.category)}</div>
            <p class="tool-description">${tool.description}</p>
        </div>
        
        <div class="tool-features">
            <div class="features-title">Key Features</div>
            <ul class="features-list">
                ${tool.features.slice(0, 4).map(feature => `
                    <li style="--dot-color: ${tool.color};">
                        <span style="background-color: ${tool.color}; width: 6px; height: 6px; border-radius: 50%; margin-right: 0.5rem; flex-shrink: 0; display: inline-block;"></span>
                        ${feature}
                    </li>
                `).join('')}
                ${tool.features.length > 4 ? `<li style="color: #666; font-style: italic;">+${tool.features.length - 4} more features</li>` : ''}
            </ul>
        </div>
        
        <button class="tool-button" 
                style="border-color: ${tool.color}; color: ${tool.color};"
                ${tool.status === 'coming-soon' ? 'disabled' : ''}>
            ${tool.status === 'coming-soon' ? 'Coming Soon' : 'Launch Tool'}
        </button>
    `;
    
    return card;
}

// Get Category Name
function getCategoryName(category) {
    const categoryNames = {
        'network': 'Network Analysis',
        'security': 'Security Assessment',
        'auth': 'Authentication Testing',
        'web': 'Web Security',
        'social': 'Human Factor Testing',
        'forensics': 'Incident Response',
        'wireless': 'Wireless Security',
        'malware': 'Threat Analysis'
    };
    return categoryNames[category] || category;
}

// Initialize Filters
function initializeFilters() {
    const categoryFilter = document.getElementById('category-filter');
    if (!categoryFilter) return;
    
    categoryFilter.addEventListener('change', (e) => {
        const selectedCategory = e.target.value;
        
        if (selectedCategory === 'all') {
            renderTools(tools);
        } else {
            const filteredTools = tools.filter(tool => tool.category === selectedCategory);
            renderTools(filteredTools);
        }
    });
}
