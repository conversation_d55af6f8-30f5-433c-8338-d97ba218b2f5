'use client';

import { Canvas } from '@react-three/fiber';
import { OrbitControls, Text, Box, Sphere, Torus } from '@react-three/drei';
import { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

function FloatingCube({ position, color, speed }: { position: [number, number, number], color: string, speed: number }) {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x += speed;
      meshRef.current.rotation.y += speed * 0.7;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * speed * 2) * 0.5;
    }
  });

  return (
    <Box ref={meshRef} position={position} args={[1, 1, 1]}>
      <meshStandardMaterial 
        color={color} 
        transparent 
        opacity={0.7}
        emissive={color}
        emissiveIntensity={0.2}
      />
    </Box>
  );
}

function CyberGrid() {
  const points = useMemo(() => {
    const temp = [];
    for (let i = -10; i <= 10; i += 2) {
      for (let j = -10; j <= 10; j += 2) {
        temp.push(new THREE.Vector3(i, 0, j));
      }
    }
    return temp;
  }, []);

  return (
    <group>
      {points.map((point, index) => (
        <Sphere key={index} position={[point.x, point.y, point.z]} args={[0.05]}>
          <meshBasicMaterial color="#00ff41" />
        </Sphere>
      ))}
    </group>
  );
}

function RotatingTorus() {
  const torusRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (torusRef.current) {
      torusRef.current.rotation.x += 0.01;
      torusRef.current.rotation.y += 0.02;
    }
  });

  return (
    <Torus ref={torusRef} args={[3, 0.5, 16, 100]} position={[0, 0, -5]}>
      <meshStandardMaterial 
        color="#0066ff" 
        transparent 
        opacity={0.6}
        wireframe
      />
    </Torus>
  );
}

function CyberText() {
  return (
    <Text
      position={[0, 2, 0]}
      fontSize={1.5}
      color="#00ff41"
      anchorX="center"
      anchorY="middle"
      font="/fonts/orbitron.woff"
    >
      EXUS HUNTER
      <meshStandardMaterial 
        emissive="#00ff41"
        emissiveIntensity={0.5}
      />
    </Text>
  );
}

export default function Hero3D() {
  return (
    <div className="w-full h-screen relative">
      <Canvas
        camera={{ position: [0, 5, 10], fov: 60 }}
        className="bg-transparent"
      >
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={1} color="#00ff41" />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#0066ff" />
        
        <CyberText />
        <CyberGrid />
        <RotatingTorus />
        
        <FloatingCube position={[-4, 1, 2]} color="#00ff41" speed={0.02} />
        <FloatingCube position={[4, -1, 1]} color="#0066ff" speed={0.015} />
        <FloatingCube position={[0, 3, -2]} color="#ff0066" speed={0.025} />
        <FloatingCube position={[-2, -2, 3]} color="#00ffff" speed={0.018} />
        
        <OrbitControls 
          enableZoom={false} 
          enablePan={false}
          autoRotate
          autoRotateSpeed={0.5}
        />
      </Canvas>
      
      {/* Overlay content */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center z-10">
          <h1 className="text-6xl md:text-8xl font-cyber font-bold text-cyber-primary text-glow mb-4">
            EXUS HUNTER
          </h1>
          <p className="text-xl md:text-2xl text-cyber-secondary mb-8 max-w-2xl mx-auto">
            Advanced Cybersecurity Tools Suite
          </p>
          <div className="text-lg text-cyber-glow">
            Professional • Secure • Innovative
          </div>
        </div>
      </div>
      
      {/* Scanning line effect */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
        <div className="scan-line absolute top-1/3"></div>
      </div>
    </div>
  );
}
