'use client';

import { motion } from 'framer-motion';
import { Tool } from '../data/tools';
import { 
  Network, 
  ShieldAlert, 
  Key, 
  Globe, 
  Users, 
  Search, 
  Wifi, 
  Bug,
  LucideIcon
} from 'lucide-react';

const iconMap: Record<string, LucideIcon> = {
  network: Network,
  'shield-alert': ShieldAlert,
  key: Key,
  globe: Globe,
  users: Users,
  search: Search,
  wifi: Wifi,
  bug: Bug,
};

interface ToolCardProps {
  tool: Tool;
  index: number;
}

export default function ToolCard({ tool, index }: ToolCardProps) {
  const IconComponent = iconMap[tool.icon] || Network;
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-cyber-primary border-cyber-primary';
      case 'beta': return 'text-yellow-400 border-yellow-400';
      case 'coming-soon': return 'text-gray-400 border-gray-400';
      default: return 'text-cyber-primary border-cyber-primary';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'beginner': return 'bg-green-500';
      case 'intermediate': return 'bg-yellow-500';
      case 'advanced': return 'bg-orange-500';
      case 'expert': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateX: -15 }}
      animate={{ opacity: 1, y: 0, rotateX: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        scale: 1.05, 
        rotateY: 5,
        z: 50,
        transition: { duration: 0.3 }
      }}
      className="cyber-card p-6 rounded-lg h-full perspective-1000 transform-style-3d"
      style={{
        background: `linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(${tool.color.slice(1)}, 0.1) 100%)`,
        borderColor: tool.color,
      }}
    >
      {/* Status Badge */}
      <div className="flex justify-between items-start mb-4">
        <div className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wider ${getStatusColor(tool.status)}`}>
          {tool.status.replace('-', ' ')}
        </div>
        <div className={`w-3 h-3 rounded-full ${getComplexityColor(tool.complexity)}`} 
             title={`Complexity: ${tool.complexity}`}>
        </div>
      </div>

      {/* Icon */}
      <div className="flex justify-center mb-6">
        <div 
          className="p-4 rounded-full border-2 cyber-glow"
          style={{ borderColor: tool.color, color: tool.color }}
        >
          <IconComponent size={48} />
        </div>
      </div>

      {/* Content */}
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-cyber-primary mb-2 font-cyber">
          {tool.name}
        </h3>
        <p className="text-sm text-cyber-secondary mb-3 font-semibold">
          {tool.category}
        </p>
        <p className="text-gray-300 text-sm leading-relaxed">
          {tool.description}
        </p>
      </div>

      {/* Features */}
      <div className="mb-6">
        <h4 className="text-cyber-primary font-semibold mb-3 text-sm uppercase tracking-wider">
          Key Features
        </h4>
        <ul className="space-y-2">
          {tool.features.slice(0, 4).map((feature, idx) => (
            <motion.li 
              key={idx}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: (index * 0.1) + (idx * 0.05) }}
              className="text-xs text-gray-400 flex items-center"
            >
              <div 
                className="w-2 h-2 rounded-full mr-2 flex-shrink-0"
                style={{ backgroundColor: tool.color }}
              ></div>
              {feature}
            </motion.li>
          ))}
          {tool.features.length > 4 && (
            <li className="text-xs text-gray-500 italic">
              +{tool.features.length - 4} more features
            </li>
          )}
        </ul>
      </div>

      {/* Action Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="w-full cyber-button relative overflow-hidden"
        style={{ borderColor: tool.color, color: tool.color }}
        disabled={tool.status === 'coming-soon'}
      >
        <span className="relative z-10">
          {tool.status === 'coming-soon' ? 'Coming Soon' : 'Launch Tool'}
        </span>
      </motion.button>

      {/* Hover Effect Overlay */}
      <motion.div
        className="absolute inset-0 rounded-lg pointer-events-none"
        initial={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
        style={{
          background: `linear-gradient(45deg, transparent, ${tool.color}10, transparent)`,
        }}
      />
    </motion.div>
  );
}
