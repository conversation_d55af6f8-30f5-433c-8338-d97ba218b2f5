<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EXUS Hunter - Autonomous Penetration Testing Platform</title>
    <meta name="description" content="EXUS Hunter autonomously finds, exploits and reports vulnerabilities in web applications. The first platform that matches the capabilities of top human pentesters.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-logo">
                    <i data-lucide="crosshair" class="brand-icon"></i>
                    <span class="brand-text">EXUS HUNTER</span>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#platform" class="nav-link">Platform</a>
                <a href="#capabilities" class="nav-link">Capabilities</a>
                <a href="#benchmarks" class="nav-link">Benchmarks</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#contact" class="nav-link">Contact</a>
                <button class="btn-primary">Get Started</button>
            </div>
            
            <div class="nav-toggle">
                <i data-lucide="menu"></i>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i data-lucide="shield-check"></i>
                    <span>Autonomous Penetration Testing</span>
                </div>
                
                <h1 class="hero-title">
                    The first platform that matches the capabilities of 
                    <span class="gradient-text">top human pentesters</span>
                </h1>
                
                <p class="hero-description">
                    EXUS Hunter autonomously finds, exploits and reports vulnerabilities in web applications. 
                    It passes 75% of web security benchmarks, revolutionizing cybersecurity testing.
                </p>
                
                <div class="hero-actions">
                    <button class="btn-primary btn-large">
                        <i data-lucide="play"></i>
                        Start Free Trial
                    </button>
                    <button class="btn-secondary btn-large">
                        <i data-lucide="calendar"></i>
                        Book Demo
                    </button>
                </div>
                
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">75%</div>
                        <div class="stat-label">Security Benchmark Pass Rate</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">10x</div>
                        <div class="stat-label">Faster Than Manual Testing</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Continuous Monitoring</div>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="terminal-window">
                    <div class="terminal-header">
                        <div class="terminal-controls">
                            <span class="control red"></span>
                            <span class="control yellow"></span>
                            <span class="control green"></span>
                        </div>
                        <div class="terminal-title">EXUS Hunter Console</div>
                    </div>
                    <div class="terminal-body">
                        <div class="terminal-line">
                            <span class="prompt">exus@hunter:~$</span>
                            <span class="command">./exus-hunter --target https://example.com</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output">🔍 Scanning target application...</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output success">✓ SQL Injection vulnerability found</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output success">✓ XSS vulnerability exploited</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output warning">⚠ CSRF token bypass detected</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output">📊 Generating comprehensive report...</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output success">✓ Report saved to /reports/scan_2024.pdf</span>
                        </div>
                        <div class="terminal-line">
                            <span class="prompt">exus@hunter:~$</span>
                            <span class="cursor">_</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Section -->
    <section id="platform" class="platform">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Autonomous Security Testing Platform</h2>
                <p class="section-description">
                    EXUS Hunter combines advanced AI with proven penetration testing methodologies 
                    to deliver comprehensive security assessments without human intervention.
                </p>
            </div>
            
            <div class="platform-grid">
                <div class="platform-card">
                    <div class="card-icon">
                        <i data-lucide="brain"></i>
                    </div>
                    <h3 class="card-title">AI-Powered Detection</h3>
                    <p class="card-description">
                        Advanced machine learning algorithms identify vulnerabilities with 
                        the precision of senior security researchers.
                    </p>
                </div>
                
                <div class="platform-card">
                    <div class="card-icon">
                        <i data-lucide="zap"></i>
                    </div>
                    <h3 class="card-title">Automated Exploitation</h3>
                    <p class="card-description">
                        Automatically exploits discovered vulnerabilities to prove impact 
                        and eliminate false positives.
                    </p>
                </div>
                
                <div class="platform-card">
                    <div class="card-icon">
                        <i data-lucide="file-text"></i>
                    </div>
                    <h3 class="card-title">Comprehensive Reporting</h3>
                    <p class="card-description">
                        Generates detailed reports with proof-of-concept exploits, 
                        risk assessments, and remediation guidance.
                    </p>
                </div>
                
                <div class="platform-card">
                    <div class="card-icon">
                        <i data-lucide="shield"></i>
                    </div>
                    <h3 class="card-title">Continuous Monitoring</h3>
                    <p class="card-description">
                        24/7 security monitoring ensures new vulnerabilities are 
                        detected and reported immediately.
                    </p>
                </div>
                
                <div class="platform-card">
                    <div class="card-icon">
                        <i data-lucide="layers"></i>
                    </div>
                    <h3 class="card-title">Multi-Vector Testing</h3>
                    <p class="card-description">
                        Tests web applications, APIs, and infrastructure using 
                        multiple attack vectors and techniques.
                    </p>
                </div>
                
                <div class="platform-card">
                    <div class="card-icon">
                        <i data-lucide="trending-up"></i>
                    </div>
                    <h3 class="card-title">Scalable Architecture</h3>
                    <p class="card-description">
                        Cloud-native platform scales to test thousands of applications 
                        simultaneously across your organization.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Capabilities Section -->
    <section id="capabilities" class="capabilities">
        <div class="container">
            <div class="capabilities-content">
                <div class="capabilities-text">
                    <h2 class="section-title">Matches Top Human Pentesters</h2>
                    <p class="section-description">
                        In comprehensive benchmarks against 104 realistic web security challenges, 
                        EXUS Hunter demonstrated performance equivalent to senior penetration testers.
                    </p>
                    
                    <div class="capability-list">
                        <div class="capability-item">
                            <i data-lucide="check-circle"></i>
                            <div>
                                <h4>SQL Injection Detection & Exploitation</h4>
                                <p>Identifies and exploits complex SQL injection vulnerabilities across multiple database types</p>
                            </div>
                        </div>
                        
                        <div class="capability-item">
                            <i data-lucide="check-circle"></i>
                            <div>
                                <h4>Cross-Site Scripting (XSS) Analysis</h4>
                                <p>Detects reflected, stored, and DOM-based XSS vulnerabilities with proof-of-concept payloads</p>
                            </div>
                        </div>
                        
                        <div class="capability-item">
                            <i data-lucide="check-circle"></i>
                            <div>
                                <h4>Authentication & Authorization Bypass</h4>
                                <p>Tests for privilege escalation, session management flaws, and access control issues</p>
                            </div>
                        </div>
                        
                        <div class="capability-item">
                            <i data-lucide="check-circle"></i>
                            <div>
                                <h4>Business Logic Vulnerability Assessment</h4>
                                <p>Identifies complex business logic flaws that traditional scanners miss</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="capabilities-visual">
                    <div class="benchmark-chart">
                        <div class="chart-header">
                            <h3>Security Benchmark Results</h3>
                            <p>Performance vs Human Pentesters</p>
                        </div>
                        <div class="chart-bars">
                            <div class="chart-bar">
                                <div class="bar-label">EXUS Hunter</div>
                                <div class="bar-container">
                                    <div class="bar-fill" style="width: 75%"></div>
                                    <span class="bar-value">75%</span>
                                </div>
                            </div>
                            <div class="chart-bar">
                                <div class="bar-label">Senior Pentester</div>
                                <div class="bar-container">
                                    <div class="bar-fill" style="width: 78%"></div>
                                    <span class="bar-value">78%</span>
                                </div>
                            </div>
                            <div class="chart-bar">
                                <div class="bar-label">Mid-level Pentester</div>
                                <div class="bar-container">
                                    <div class="bar-fill" style="width: 65%"></div>
                                    <span class="bar-value">65%</span>
                                </div>
                            </div>
                            <div class="chart-bar">
                                <div class="bar-label">Traditional Scanner</div>
                                <div class="bar-container">
                                    <div class="bar-fill traditional" style="width: 35%"></div>
                                    <span class="bar-value">35%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">Ready to revolutionize your security testing?</h2>
                <p class="cta-description">
                    Join leading organizations using EXUS Hunter to secure their applications 
                    with autonomous penetration testing.
                </p>
                <div class="cta-actions">
                    <button class="btn-primary btn-large">
                        <i data-lucide="rocket"></i>
                        Start Free Trial
                    </button>
                    <button class="btn-secondary btn-large">
                        <i data-lucide="phone"></i>
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="brand-logo">
                        <i data-lucide="crosshair" class="brand-icon"></i>
                        <span class="brand-text">EXUS HUNTER</span>
                    </div>
                    <p class="footer-description">
                        Autonomous penetration testing platform that matches 
                        the capabilities of top human security researchers.
                    </p>
                </div>
                
                <div class="footer-links">
                    <div class="footer-column">
                        <h4>Platform</h4>
                        <a href="#capabilities">Capabilities</a>
                        <a href="#benchmarks">Benchmarks</a>
                        <a href="#integrations">Integrations</a>
                        <a href="#api">API</a>
                    </div>
                    
                    <div class="footer-column">
                        <h4>Company</h4>
                        <a href="#about">About</a>
                        <a href="#careers">Careers</a>
                        <a href="#blog">Blog</a>
                        <a href="#press">Press</a>
                    </div>
                    
                    <div class="footer-column">
                        <h4>Support</h4>
                        <a href="#docs">Documentation</a>
                        <a href="#help">Help Center</a>
                        <a href="#contact">Contact</a>
                        <a href="#status">Status</a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 EXUS Hunter. All rights reserved.</p>
                <div class="footer-legal">
                    <a href="#privacy">Privacy Policy</a>
                    <a href="#terms">Terms of Service</a>
                    <a href="#security">Security</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
