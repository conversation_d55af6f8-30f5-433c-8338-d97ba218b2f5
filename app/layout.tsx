import './globals.css'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'EXUS Hunter - Advanced Cybersecurity Tools',
  description: 'Professional cybersecurity and penetration testing tools suite with cutting-edge 3D interface',
  keywords: 'cybersecurity, penetration testing, security tools, EXUS Hunter, ethical hacking',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link 
          href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" 
          rel="stylesheet" 
        />
      </head>
      <body className="bg-cyber-darker text-cyber-primary font-mono overflow-x-hidden">
        {children}
      </body>
    </html>
  )
}
