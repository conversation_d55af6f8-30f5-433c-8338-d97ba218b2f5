'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Hero3D from '../components/Hero3D';
import ToolCard from '../components/ToolCard';
import CyberBackground from '../components/CyberBackground';
import { tools, categories } from '../data/tools';
import { Filter, ChevronDown, Terminal, Shield, Zap } from 'lucide-react';

export default function Home() {
  const [selectedCategory, setSelectedCategory] = useState('All Tools');
  const [filteredTools, setFilteredTools] = useState(tools);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  useEffect(() => {
    if (selectedCategory === 'All Tools') {
      setFilteredTools(tools);
    } else {
      setFilteredTools(tools.filter(tool => tool.category === selectedCategory));
    }
  }, [selectedCategory]);

  const stats = [
    { label: 'Security Tools', value: '8+', icon: Shield },
    { label: 'Active Scans', value: '24/7', icon: Terminal },
    { label: 'Threat Detection', value: '99.9%', icon: Zap },
  ];

  return (
    <main className="relative min-h-screen">
      <CyberBackground />
      
      {/* Hero Section */}
      <section className="relative z-10">
        <Hero3D />
      </section>

      {/* Stats Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
                className="cyber-card p-8 text-center"
              >
                <div className="flex justify-center mb-4">
                  <stat.icon size={48} className="text-cyber-primary cyber-glow" />
                </div>
                <div className="text-4xl font-bold text-cyber-primary mb-2 font-cyber">
                  {stat.value}
                </div>
                <div className="text-cyber-secondary uppercase tracking-wider text-sm">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Tools Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl font-bold text-cyber-primary mb-6 font-cyber text-glow">
              SECURITY ARSENAL
            </h2>
            <p className="text-xl text-cyber-secondary max-w-3xl mx-auto">
              Professional-grade cybersecurity tools designed for penetration testing, 
              vulnerability assessment, and security research.
            </p>
          </motion.div>

          {/* Category Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex justify-center mb-12"
          >
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="cyber-button appearance-none bg-transparent pr-10 cursor-pointer"
              >
                {categories.map(category => (
                  <option 
                    key={category} 
                    value={category}
                    className="bg-cyber-dark text-cyber-primary"
                  >
                    {category}
                  </option>
                ))}
              </select>
              <ChevronDown 
                size={20} 
                className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-cyber-primary" 
              />
            </div>
          </motion.div>

          {/* Tools Grid */}
          <motion.div
            layout
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
          >
            {filteredTools.map((tool, index) => (
              <ToolCard key={tool.id} tool={tool} index={index} />
            ))}
          </motion.div>

          {/* No tools message */}
          {filteredTools.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <div className="text-cyber-secondary text-xl">
                No tools found in this category.
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 py-12 px-4 border-t border-cyber-primary/20">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <h3 className="text-2xl font-bold text-cyber-primary mb-4 font-cyber">
              EXUS HUNTER
            </h3>
            <p className="text-cyber-secondary mb-6">
              Advanced Cybersecurity Tools Suite for Professional Security Testing
            </p>
            <div className="text-sm text-gray-400">
              © 2024 EXUS Hunter. Professional Use Only. Educational and Authorized Testing Purposes.
            </div>
          </motion.div>
        </div>
      </footer>
    </main>
  );
}
