@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    background: #050505;
    background-image: 
      radial-gradient(circle at 25% 25%, #003d10 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, #001a33 0%, transparent 50%);
    min-height: 100vh;
  }
}

@layer components {
  .cyber-border {
    border: 1px solid #00ff41;
    box-shadow: 
      0 0 10px rgba(0, 255, 65, 0.3),
      inset 0 0 10px rgba(0, 255, 65, 0.1);
  }
  
  .cyber-glow {
    box-shadow: 
      0 0 5px #00ff41,
      0 0 10px #00ff41,
      0 0 15px #00ff41,
      0 0 20px #00ff41;
  }
  
  .text-glow {
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
  }
  
  .matrix-bg {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(0, 255, 65, 0.1) 50%,
      transparent 100%
    );
  }
  
  .scan-line {
    background: linear-gradient(
      90deg,
      transparent 0%,
      #00ff41 50%,
      transparent 100%
    );
    height: 2px;
    width: 100%;
    animation: scan 2s linear infinite;
  }
  
  .cyber-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 65, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }
  
  .cyber-card:hover {
    border-color: #00ff41;
    box-shadow: 
      0 0 20px rgba(0, 255, 65, 0.4),
      inset 0 0 20px rgba(0, 255, 65, 0.1);
    transform: translateY(-5px);
  }
  
  .cyber-button {
    background: linear-gradient(45deg, transparent, rgba(0, 255, 65, 0.1), transparent);
    border: 1px solid #00ff41;
    color: #00ff41;
    padding: 12px 24px;
    text-transform: uppercase;
    letter-spacing: 2px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .cyber-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  .cyber-button:hover::before {
    left: 100%;
  }
  
  .cyber-button:hover {
    box-shadow: 
      0 0 20px rgba(0, 255, 65, 0.5),
      inset 0 0 20px rgba(0, 255, 65, 0.1);
    text-shadow: 0 0 10px #00ff41;
  }
}

@layer utilities {
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .transform-style-3d {
    transform-style: preserve-3d;
  }
  
  .backface-hidden {
    backface-visibility: hidden;
  }
}
