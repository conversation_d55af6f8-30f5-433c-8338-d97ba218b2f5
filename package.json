{"name": "exus-hunter-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.0.0", "three": "^0.158.0", "@react-three/fiber": "^8.15.0", "@react-three/drei": "^9.88.0", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0"}, "devDependencies": {"typescript": "^5.2.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/three": "^0.158.0", "tailwindcss": "^3.3.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0"}}