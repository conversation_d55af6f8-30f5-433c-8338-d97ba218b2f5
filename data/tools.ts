export interface Tool {
  id: string;
  name: string;
  category: string;
  description: string;
  features: string[];
  icon: string;
  color: string;
  status: 'active' | 'beta' | 'coming-soon';
  complexity: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

export const tools: Tool[] = [
  {
    id: 'network-scanner',
    name: 'Network Scanner',
    category: 'Network Analysis',
    description: 'Advanced network discovery and port scanning tool with stealth capabilities',
    features: [
      'TCP/UDP Port Scanning',
      'Service Detection',
      'OS Fingerprinting',
      'Stealth Scan Modes',
      'Network Topology Mapping',
      'Real-time Monitoring'
    ],
    icon: 'network',
    color: '#00ff41',
    status: 'active',
    complexity: 'intermediate'
  },
  {
    id: 'vulnerability-scanner',
    name: 'Vulnerability Scanner',
    category: 'Security Assessment',
    description: 'Comprehensive vulnerability assessment with AI-powered threat detection',
    features: [
      'CVE Database Integration',
      'Zero-day Detection',
      'Risk Assessment',
      'Automated Reporting',
      'Compliance Checking',
      'Patch Management'
    ],
    icon: 'shield-alert',
    color: '#ff0066',
    status: 'active',
    complexity: 'advanced'
  },
  {
    id: 'password-cracker',
    name: 'Password Cracker',
    category: 'Authentication Testing',
    description: 'High-performance password recovery and strength testing suite',
    features: [
      'Dictionary Attacks',
      'Brute Force Methods',
      'Rainbow Tables',
      'Hash Analysis',
      'GPU Acceleration',
      'Custom Wordlists'
    ],
    icon: 'key',
    color: '#0066ff',
    status: 'active',
    complexity: 'expert'
  },
  {
    id: 'web-app-tester',
    name: 'Web Application Tester',
    category: 'Web Security',
    description: 'Automated web application security testing with OWASP compliance',
    features: [
      'SQL Injection Detection',
      'XSS Vulnerability Scanning',
      'CSRF Testing',
      'Authentication Bypass',
      'API Security Testing',
      'OWASP Top 10 Coverage'
    ],
    icon: 'globe',
    color: '#00ffff',
    status: 'active',
    complexity: 'intermediate'
  },
  {
    id: 'social-engineering',
    name: 'Social Engineering Toolkit',
    category: 'Human Factor Testing',
    description: 'Comprehensive social engineering assessment and awareness training',
    features: [
      'Phishing Campaigns',
      'Email Templates',
      'Social Media OSINT',
      'Awareness Training',
      'Reporting Dashboard',
      'Compliance Tracking'
    ],
    icon: 'users',
    color: '#ff6600',
    status: 'beta',
    complexity: 'advanced'
  },
  {
    id: 'forensics-analyzer',
    name: 'Digital Forensics Analyzer',
    category: 'Incident Response',
    description: 'Advanced digital forensics and incident response capabilities',
    features: [
      'Memory Analysis',
      'Disk Imaging',
      'Network Forensics',
      'Timeline Analysis',
      'Evidence Chain',
      'Report Generation'
    ],
    icon: 'search',
    color: '#9900ff',
    status: 'active',
    complexity: 'expert'
  },
  {
    id: 'wireless-auditor',
    name: 'Wireless Security Auditor',
    category: 'Wireless Security',
    description: 'Comprehensive wireless network security assessment tool',
    features: [
      'WiFi Network Discovery',
      'WPA/WPA2 Testing',
      'Bluetooth Analysis',
      'Rogue AP Detection',
      'Signal Analysis',
      'Encryption Testing'
    ],
    icon: 'wifi',
    color: '#ffff00',
    status: 'coming-soon',
    complexity: 'advanced'
  },
  {
    id: 'malware-analyzer',
    name: 'Malware Analyzer',
    category: 'Threat Analysis',
    description: 'Advanced malware detection and behavioral analysis platform',
    features: [
      'Static Analysis',
      'Dynamic Analysis',
      'Sandbox Environment',
      'Threat Intelligence',
      'IOC Extraction',
      'Family Classification'
    ],
    icon: 'bug',
    color: '#ff3300',
    status: 'beta',
    complexity: 'expert'
  }
];

export const categories = [
  'All Tools',
  'Network Analysis',
  'Security Assessment',
  'Authentication Testing',
  'Web Security',
  'Human Factor Testing',
  'Incident Response',
  'Wireless Security',
  'Threat Analysis'
];
